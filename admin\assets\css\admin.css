/*
 * Modern Admin Panel - Santa Casa da Misericórdia da Covilhã
 * Fixed Left Sidebar Layout
 */

/* Reset e Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2c5aa0;
    --primary-dark: #1e3d6f;
    --secondary-color: #34495e;
    --accent-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;

    --sidebar-bg: #1a252f;
    --sidebar-hover: #243447;
    --sidebar-active: #ffffff;
    --sidebar-text: #b8c5d1;
    --sidebar-text-muted: #7a8a99;
    --sidebar-border: #2d3e50;

    --content-bg: #f8f9fa;
    --card-bg: #ffffff;
    --border-color: #dee2e6;
    --text-color: #2c3e50;
    --text-muted: #6c757d;

    --header-height: 70px;
    --sidebar-width: 280px;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --box-shadow-hover: 0 4px 20px rgba(0,0,0,0.15);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--content-bg);
    color: var(--text-color);
    line-height: 1.6;
    font-size: 14px;
}

/* Layout Container */
.admin-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.admin-sidebar {
    width: var(--sidebar-width);
    background: var(--sidebar-bg);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: var(--transition);
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid var(--sidebar-border);
    text-align: center;
}

.sidebar-logo {
    color: #ffffff;
    font-size: 1.3rem;
    font-weight: 700;
    text-decoration: none;
    line-height: 1.2;
    display: block;
}

/* Enhanced logo styling for better visibility */
.leftside-menu .logo {
    display: block;
    padding: 1.5rem 1.5rem 1rem 1.5rem; /* Added top padding for breathing room */
    text-align: center;
    border-bottom: 1px solid var(--sidebar-border);
}

.leftside-menu .logo img {
    max-width: 100%;
    height: auto;
    transition: all 0.3s ease;
}

/* Ensure logos maintain aspect ratio on smaller screens */
@media (max-width: 768px) {
    .leftside-menu .logo {
        padding: 1rem 1rem 0.75rem 1rem; /* Reduced padding on mobile */
    }

    .leftside-menu .logo-lg img {
        max-height: 35px; /* Slightly smaller on mobile */
    }

    .leftside-menu .logo-sm img {
        max-height: 35px; /* Slightly smaller on mobile */
    }
}

/* Enhanced active navigation styling for better contrast - Force white background */
.leftside-menu .side-nav .nav-link.active,
.leftside-menu .side-nav .side-nav-link.active,
.side-nav .side-nav-link.active {
    background-color: #ffffff !important;
    color: #1a252f !important;
    font-weight: 600 !important;
    border-radius: 6px !important;
    margin: 2px 8px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

/* Ensure icons in active nav items are also dark */
.leftside-menu .side-nav .nav-link.active i,
.leftside-menu .side-nav .side-nav-link.active i,
.side-nav .side-nav-link.active i {
    color: #1a252f !important;
}

.sidebar-subtitle {
    color: var(--sidebar-text-muted);
    font-size: 0.85rem;
    margin-top: 0.5rem;
    font-weight: 400;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-section {
    margin-bottom: 2rem;
}

.nav-section-title {
    color: var(--sidebar-text-muted);
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1.2px;
    padding: 0 1.5rem;
    margin-bottom: 1rem;
}

.nav-item {
    margin-bottom: 0.125rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.875rem 1.5rem;
    color: var(--sidebar-text);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
    font-weight: 500;
    font-size: 0.9rem;
    border-radius: 0;
}

/* Sidebar navigation hover - only apply to sidebar nav-links */
.leftside-menu .nav-link:hover,
.side-nav .nav-link:hover {
    background-color: var(--sidebar-hover);
    color: #ffffff;
}

/* Topbar dropdown hover effects - clean and professional */
.topbar-menu .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    transition: all 0.2s ease;
}

/* Specific hover for notification dropdown */
.notification-list .nav-link:hover {
    background-color: rgba(44, 90, 160, 0.1);
    border-radius: 6px;
}

/* Specific hover for user dropdown */
.nav-user:hover {
    background-color: rgba(44, 90, 160, 0.1);
    border-radius: 6px;
}

/* Active navigation indicator - left border accent */
.leftside-menu .side-nav .side-nav-link.active::before,
.side-nav .side-nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: #2c5aa0 !important;
    border-radius: 0 4px 4px 0;
}

/* Override any theme-specific active states */
.leftside-menu .side-nav .side-nav-item .side-nav-link.active {
    background-color: #ffffff !important;
    color: #1a252f !important;
}

.nav-icon {
    width: 18px;
    height: 18px;
    margin-right: 0.875rem;
    opacity: 0.7;
    font-size: 0.9rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Main Content */
.admin-main {
    flex: 1;
    margin-left: var(--sidebar-width);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Header */
.admin-header {
    background: var(--card-bg);
    height: var(--header-height);
    border-bottom: 2px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    position: sticky;
    top: 0;
    z-index: 999;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.header-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-menu {
    position: relative;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.875rem;
    padding: 0.625rem 1.125rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid transparent;
}

.user-info:hover {
    background-color: var(--content-bg);
    border-color: var(--border-color);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 0.95rem;
    box-shadow: 0 2px 8px rgba(44, 90, 160, 0.3);
}

.user-details {
    display: flex;
    flex-direction: column;
    line-height: 1.3;
}

.user-name {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.95rem;
}

.user-role {
    color: var(--text-muted);
    font-size: 0.8rem;
    font-weight: 500;
}

.dropdown-arrow {
    color: var(--text-muted);
    font-size: 0.7rem;
    margin-left: 0.5rem;
    transition: var(--transition);
}

.user-info:hover .dropdown-arrow {
    color: var(--primary-color);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-hover);
    min-width: 220px;
    display: none;
    z-index: 1001;
    margin-top: 0.5rem;
    opacity: 0;
    transform: translateY(-10px);
    transition: var(--transition);
}

.user-dropdown.show {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: 0.875rem 1.25rem;
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9rem;
    font-weight: 500;
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background-color: var(--primary-color);
    color: #ffffff;
}

/* Content Area */
.admin-content {
    flex: 1;
    padding: 2rem;
    background-color: var(--content-bg);
}

/* Page Header */
.page-header {
    margin-bottom: 2.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.page-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 0.75rem;
    line-height: 1.2;
}

.page-subtitle {
    color: var(--text-muted);
    font-size: 1.1rem;
    font-weight: 400;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Widget Cards */
.widget-flat {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s;
}

.widget-flat:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.widget-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #6c757d;
    background-color: rgba(108, 117, 125, 0.1);
}

.widget-icon.bg-primary {
    color: #2c5aa0;
    background-color: rgba(44, 90, 160, 0.1);
}

.widget-icon.bg-success {
    color: #198754;
    background-color: rgba(25, 135, 84, 0.1);
}

.widget-icon.bg-info {
    color: #0dcaf0;
    background-color: rgba(13, 202, 240, 0.1);
}

.widget-icon.bg-warning {
    color: #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
}

.widget-icon.bg-danger {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
}

.stat-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border-left: 4px solid var(--primary-color);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-hover);
}

.stat-card.success {
    border-left-color: var(--success-color);
}

.stat-card.warning {
    border-left-color: var(--warning-color);
}

.stat-card.danger {
    border-left-color: var(--danger-color);
}

.stat-card.info {
    border-left-color: var(--info-color);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-muted);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Cards */
.card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--box-shadow-hover);
}

.card-header {
    background: var(--card-bg);
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.card-body {
    padding: 1.5rem;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background: var(--card-bg);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.form-control.is-valid {
    border-color: var(--success-color);
}

/* Enhanced Form Validation Messages */
.invalid-feedback {
    display: block;
    color: var(--danger-color);
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.5rem;
    padding: 0.5rem 0.75rem;
    background-color: rgba(231, 76, 60, 0.1);
    border: 1px solid rgba(231, 76, 60, 0.2);
    border-radius: 6px;
    position: relative;
    line-height: 1.4;
}

.invalid-feedback::before {
    content: '⚠';
    margin-right: 0.5rem;
    font-weight: bold;
    color: var(--danger-color);
}

.valid-feedback {
    display: block;
    color: var(--success-color);
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.5rem;
    padding: 0.5rem 0.75rem;
    background-color: rgba(39, 174, 96, 0.1);
    border: 1px solid rgba(39, 174, 96, 0.2);
    border-radius: 6px;
    position: relative;
    line-height: 1.4;
}

.valid-feedback::before {
    content: '✓';
    margin-right: 0.5rem;
    font-weight: bold;
    color: var(--success-color);
}

/* Enhanced form field validation states */
.form-control.is-invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23e74c3c'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem 1rem;
    padding-right: 2.5rem;
}

.form-control.is-valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2327ae60' d='m2.3 6.73.94-.94 2.94-2.94.94-.94.94.94L2.3 8.67z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem 1rem;
    padding-right: 2.5rem;
}

/* Subtle error state for non-intrusive validation */
.form-control.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23e74c3c'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem 1rem;
    padding-right: 2.5rem;
}

/* Tooltip de erro de campo */
.field-error-tooltip {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--danger-color);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    z-index: 1000;
    margin-top: 4px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    gap: 6px;
    animation: fadeInTooltip 0.3s ease-in-out;
}

.field-error-tooltip::before {
    content: '';
    position: absolute;
    top: -4px;
    left: 12px;
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid var(--danger-color);
}

.field-error-tooltip i {
    font-size: 11px;
}

/* Animações para tooltips */
@keyframes fadeInTooltip {
    from {
        opacity: 0;
        transform: translateY(-8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOutTooltip {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-8px);
    }
}

/* Dropdown Fixes */
.dropdown-menu {
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    border-radius: 8px;
    padding: 0.5rem 0;
    z-index: 1050;
    transition: opacity 0.15s ease, transform 0.15s ease;
}

/* Let Bootstrap handle the show/hide states naturally */

/* Fix for notification dropdown */
.notification-list .dropdown-menu {
    min-width: 320px;
    max-width: 400px;
}

/* Fix for user dropdown */
.nav-user + .dropdown-menu {
    min-width: 200px;
}

/* Prevent dark overlay issues - but allow Bootstrap's natural behavior */
.dropdown-backdrop {
    display: none !important;
}

/* Remove any modal backdrop that might interfere */
.modal-backdrop {
    display: none !important;
}

/* Ensure dropdown toggles are clickable */
[data-bs-toggle="dropdown"] {
    cursor: pointer !important;
    pointer-events: auto !important;
}

/* Ensure dropdowns appear above other content */
.topbar-menu .dropdown {
    position: relative;
    z-index: 1051;
}

.topbar-menu .dropdown-menu {
    z-index: 1052;
}

/* Fix any overlay issues */
body.dropdown-open {
    overflow: visible !important;
}

/* Ensure proper dropdown positioning */
.dropdown-menu-end {
    right: 0 !important;
    left: auto !important;
}

/* Dropdown animation handled by Bootstrap */

/* Notification System */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 420px;
    pointer-events: none;
}

.notification-container .toast {
    pointer-events: auto;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: 8px;
    animation: slideInRight 0.3s ease-out;
    min-width: 350px;
}

.notification-container .toast.show {
    display: block;
}

.notification-container .toast.hiding {
    animation: slideOutRight 0.3s ease-in;
}

.notification-container .toast .toast-body {
    padding: 1rem;
    word-wrap: break-word;
}

.notification-container .toast .btn-close {
    padding: 0.5rem;
    margin: 0;
    flex-shrink: 0;
}

.notification-container .toast .d-flex {
    min-height: 48px;
}

/* Notification animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Progress animation for notifications */
.notification-progress {
    margin-top: 8px;
    display: flex;
    align-items: center;
}

.progress-dots {
    display: flex;
    gap: 4px;
    align-items: center;
}

.progress-dots .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.7);
    animation: dotPulse 1.4s infinite ease-in-out;
}

.progress-dots .dot:nth-child(1) {
    animation-delay: -0.32s;
}

.progress-dots .dot:nth-child(2) {
    animation-delay: -0.16s;
}

.progress-dots .dot:nth-child(3) {
    animation-delay: 0s;
}

@keyframes dotPulse {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Enhanced notification message styling */
.notification-message {
    line-height: 1.4;
    flex-grow: 1;
}

/* Enhanced confirmation modal */
#confirmation-modal .modal-dialog {
    max-width: 400px;
}

#confirmation-modal .modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

#confirmation-modal .modal-header {
    border-bottom: 1px solid #e9ecef;
    padding: 1.25rem 1.5rem 1rem;
}

#confirmation-modal .modal-body {
    padding: 1rem 1.5rem 1.25rem;
}

#confirmation-modal .modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 1rem 1.5rem 1.25rem;
    gap: 0.5rem;
}

#confirmation-modal .btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.5rem 1.25rem;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    line-height: 1;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-1px);
}

/* Enhanced Primary Button - Professional Santa Casa Branding */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border: none;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: none;
    box-shadow: 0 2px 8px rgba(44, 90, 160, 0.25);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #1a3a5c 100%);
    box-shadow: 0 6px 20px rgba(44, 90, 160, 0.4);
    transform: translateY(-2px);
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(44, 90, 160, 0.3);
}

.btn-primary:focus {
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.3);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #229954;
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
    box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.btn-secondary {
    background: var(--content-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
}

/* Tables */
.table-responsive {
    overflow-x: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    background: var(--card-bg);
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.table th {
    background-color: var(--content-bg);
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

/* Alerts */
.alert {
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1.5rem;
    border-left: 4px solid;
    background: var(--card-bg);
    box-shadow: var(--box-shadow);
}

.alert-success {
    border-left-color: var(--success-color);
    background-color: rgba(39, 174, 96, 0.1);
    color: #155724;
}

.alert-warning {
    border-left-color: var(--warning-color);
    background-color: rgba(243, 156, 18, 0.1);
    color: #856404;
}

.alert-danger {
    border-left-color: var(--danger-color);
    background-color: rgba(231, 76, 60, 0.1);
    color: #721c24;
}

.alert-info {
    border-left-color: var(--info-color);
    background-color: rgba(23, 162, 184, 0.1);
    color: #0c5460;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-muted { color: var(--text-muted); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }

/* Badge */
.badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
}

.badge-primary { background-color: var(--primary-color); color: white; }
.badge-success { background-color: var(--success-color); color: white; }
.badge-warning { background-color: var(--warning-color); color: white; }
.badge-danger { background-color: var(--danger-color); color: white; }
.badge-secondary { background-color: var(--text-muted); color: white; }

/* Login Page Improvements */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    padding: 2rem;
}

/* Authentication Background - Lighter and more welcoming */
body.authentication-bg {
    background: linear-gradient(135deg,
        rgba(44, 90, 160, 0.85) 0%,
        rgba(52, 152, 219, 0.75) 50%,
        rgba(44, 62, 80, 0.8) 100%) !important;
    background-size: cover !important;
    position: relative;
}

/* Add subtle overlay pattern for texture */
body.authentication-bg::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255,255,255,0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

.login-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-hover);
    padding: 3rem;
    width: 100%;
    max-width: 420px;
}

.login-logo {
    text-align: center;
    margin-bottom: 2rem;
}

.login-logo h1 {
    color: var(--primary-color);
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.login-logo p {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Enhanced Logo Container */
.login-logo-container {
    text-align: center;
    margin-bottom: 2.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.login-logo-container img {
    max-width: 280px;
    height: auto;
    margin-bottom: 1rem;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.login-logo-large {
    max-width: 280px;
    height: auto;
}

.password-strength {
    margin-top: 0.5rem;
}

.password-strength-bar {
    height: 4px;
    background: var(--content-bg);
    border-radius: 2px;
    overflow: hidden;
}

.password-strength-fill {
    height: 100%;
    transition: var(--transition);
    border-radius: 2px;
}

.password-strength-weak { background: var(--danger-color); width: 25%; }
.password-strength-fair { background: var(--warning-color); width: 50%; }
.password-strength-good { background: var(--accent-color); width: 75%; }
.password-strength-strong { background: var(--success-color); width: 100%; }

/* Loading */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--card-bg);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
}

.mobile-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    z-index: 999;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }

    .admin-sidebar.open {
        transform: translateX(0);
    }

    .admin-main {
        margin-left: 0;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .admin-content {
        padding: 1rem;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .header-title {
        font-size: 1.25rem;
    }

    .user-details {
        display: none;
    }

    .table th,
    .table td {
        padding: 0.5rem;
        font-size: 0.85rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }

    .login-card {
        padding: 2rem;
        margin: 1rem;
    }

    .login-logo-container img {
        max-width: 200px;
    }

    .sidebar-header {
        padding: 1rem;
    }

    .nav-link {
        padding: 1rem 1.5rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .admin-content {
        padding: 0.75rem;
    }

    .card-body {
        padding: 1rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-value {
        font-size: 2rem;
    }

    .login-card {
        padding: 1.5rem;
    }

    .login-logo-container img {
        max-width: 150px;
    }
}

/* Authentication Pages Specific Styling */
.account-pages {
    position: relative;
    z-index: 1;
}

.account-pages .card {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    border: none;
    border-radius: 12px;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

/* Enhanced form styling for auth pages */
.authentication-form .form-label {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
}

.authentication-form .form-control {
    padding: 0.875rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: #fafbfc;
}

.authentication-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
    background-color: #ffffff;
}

.authentication-form .form-control::placeholder {
    color: #9ca3af;
    font-size: 0.95rem;
}

/* Enhanced button styling for auth pages */
.authentication-form .btn {
    padding: 0.875rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 8px;
    text-transform: none;
    letter-spacing: 0.3px;
}

/* Footer styling for auth pages */
.footer.footer-alt {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    padding: 1.5rem;
    background: transparent;
    border: none;
}

.footer.footer-alt .text-light {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Link styling improvements */
.text-dark.link-offset-3 {
    color: var(--primary-color) !important;
    text-decoration: none !important;
    font-weight: 600;
    transition: all 0.3s ease;
}

.text-dark.link-offset-3:hover {
    color: var(--primary-dark) !important;
    text-decoration: none !important;
    transform: translateX(-2px);
}

/* Alert improvements for auth pages */
.alert {
    border-radius: 8px;
    border: none;
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.alert-danger {
    background-color: rgba(231, 76, 60, 0.1);
    color: #c0392b;
    border-left: 4px solid var(--danger-color);
}

.alert-success {
    background-color: rgba(39, 174, 96, 0.1);
    color: #27ae60;
    border-left: 4px solid var(--success-color);
}

/* Responsive improvements for mobile */
@media (max-width: 576px) {
    .login-logo-container img {
        max-width: 180px;
    }

    .authentication-form .form-control {
        padding: 0.75rem;
        font-size: 0.95rem;
    }

    .authentication-form .btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.95rem;
    }
}

/* Search Results */
.search-result-item:last-child {
    border-bottom: none !important;
}

.search-results .highlight {
    background-color: #fff3cd;
    padding: 0.1rem 0.2rem;
    border-radius: 0.2rem;
}

/* Additional responsive improvements for widgets */
@media (max-width: 768px) {
    .widget-flat .card-body {
        padding: 1rem;
    }

    .widget-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1.25rem;
    }

    .search-result-item {
        padding: 1rem 0 !important;
    }
}
