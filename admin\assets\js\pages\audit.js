/**
 * Audit Log JavaScript
 * Santa Casa da Misericórdia da Covilhã
 */

(function($) {
    "use strict";

    function AuditLog() {
        this.$body = $("body");
        this.init();
    }

    AuditLog.prototype.initDatePickers = function() {
        // Initialize Flatpickr date pickers
        if (typeof flatpickr !== 'undefined') {
            // Configure Portuguese locale
            flatpickr.localize(flatpickr.l10ns.pt);

            // Common configuration for both date pickers
            const commonConfig = {
                dateFormat: "d-m-Y", // Portuguese format: DD-MM-YYYY
                altInput: true,
                altFormat: "d-m-Y",
                maxDate: "today",
                locale: "pt",
                allowInput: true,
                clickOpens: true,
                // Ensure consistent styling with Bootstrap form controls
                onReady: function(selectedDates, dateStr, instance) {
                    // Add Bootstrap classes to maintain consistent styling
                    instance.input.classList.add('form-control');
                    instance.altInput.classList.add('form-control');

                    // Ensure proper height matching other form controls
                    instance.altInput.style.height = 'calc(1.5em + 0.75rem + 2px)';
                    instance.altInput.style.padding = '0.375rem 0.75rem';
                    instance.altInput.style.fontSize = '0.875rem';
                    instance.altInput.style.lineHeight = '1.5';
                    instance.altInput.style.border = '1px solid #dee2e6';
                    instance.altInput.style.borderRadius = '0.375rem';
                }
            };

            // Start date picker
            flatpickr("#filter-date-start", {
                ...commonConfig,
                placeholder: "Selecionar data início",
                onChange: function(selectedDates, dateStr, instance) {
                    // Update end date minimum
                    const endDatePicker = document.querySelector("#filter-date-end")._flatpickr;
                    if (endDatePicker && selectedDates.length > 0) {
                        endDatePicker.set('minDate', selectedDates[0]);
                    }
                }
            });

            // End date picker
            flatpickr("#filter-date-end", {
                ...commonConfig,
                placeholder: "Selecionar data fim",
                onChange: function(selectedDates, dateStr, instance) {
                    // Update start date maximum
                    const startDatePicker = document.querySelector("#filter-date-start")._flatpickr;
                    if (startDatePicker && selectedDates.length > 0) {
                        startDatePicker.set('maxDate', selectedDates[0]);
                    }
                }
            });
        }
    };

    AuditLog.prototype.initDetailsModal = function() {
        // Handle details modal
        $('#detailsModal').on('show.bs.modal', function (event) {
            const button = $(event.relatedTarget);
            const oldValues = button.data('old-values');
            const newValues = button.data('new-values');
            const action = button.data('action');
            const table = button.data('table');

            const modal = $(this);
            const modalContent = modal.find('#modal-content');

            // Clear previous content
            modalContent.empty();

            // Build content based on action type
            let content = '<div class="row">';

            // Action info with enhanced security event details
            content += '<div class="col-12 mb-3">';
            content += '<div class="alert alert-info">';
            content += '<h6 class="mb-1"><i class="ri-information-line"></i> Informação da Ação</h6>';

            // Enhanced action display
            let actionDisplay = action;
            if (action === 'security_event') {
                actionDisplay = 'Evento de Segurança';

                // Try to get more specific info from new values
                if (newValues && newValues !== '') {
                    try {
                        const newData = JSON.parse(newValues);
                        if (newData.event) {
                            switch (newData.event) {
                                case 'test_security_event':
                                    actionDisplay = 'Teste de Segurança';
                                    break;
                                case 'system_check':
                                    actionDisplay = 'Verificação do Sistema';
                                    break;
                                case 'failed_login':
                                    actionDisplay = 'Tentativa de Login Falhada';
                                    break;
                                case 'ip_blocked':
                                    actionDisplay = 'IP Bloqueado';
                                    break;
                                case 'suspicious_activity':
                                    actionDisplay = 'Atividade Suspeita';
                                    break;
                                case 'rate_limit_exceeded':
                                    actionDisplay = 'Limite de Tentativas Excedido';
                                    break;
                                case 'unauthorized_access':
                                    actionDisplay = 'Acesso Não Autorizado';
                                    break;
                            }
                        }
                    } catch (e) {
                        // Keep default if JSON parsing fails
                    }
                }
            }

            content += '<p class="mb-0"><strong>Ação:</strong> ' + actionDisplay + '</p>';
            if (table) {
                content += '<p class="mb-0"><strong>Tabela:</strong> ' + table + '</p>';
            }
            content += '</div>';
            content += '</div>';

            // Old values
            if (oldValues && oldValues !== '') {
                content += '<div class="col-md-6">';
                content += '<h6 class="text-danger"><i class="ri-subtract-line"></i> Valores Anteriores</h6>';
                content += '<div class="bg-light p-3 rounded">';
                try {
                    const oldData = JSON.parse(oldValues);
                    content += '<pre class="mb-0 text-danger">' + JSON.stringify(oldData, null, 2) + '</pre>';
                } catch (e) {
                    content += '<p class="text-muted mb-0">Dados não disponíveis</p>';
                }
                content += '</div>';
                content += '</div>';
            }

            // New values
            if (newValues && newValues !== '') {
                content += '<div class="col-md-6">';
                content += '<h6 class="text-success"><i class="ri-add-line"></i> Valores Novos</h6>';
                content += '<div class="bg-light p-3 rounded">';
                try {
                    const newData = JSON.parse(newValues);
                    content += '<pre class="mb-0 text-success">' + JSON.stringify(newData, null, 2) + '</pre>';
                } catch (e) {
                    content += '<p class="text-muted mb-0">Dados não disponíveis</p>';
                }
                content += '</div>';
                content += '</div>';
            }

            // If no data available
            if ((!oldValues || oldValues === '') && (!newValues || newValues === '')) {
                content += '<div class="col-12">';
                content += '<div class="text-center py-4">';
                content += '<i class="ri-file-list-line fs-1 text-muted d-block mb-2"></i>';
                content += '<p class="text-muted">Nenhum detalhe adicional disponível para esta ação.</p>';
                content += '</div>';
                content += '</div>';
            }

            content += '</div>';

            modalContent.html(content);
        });
    };

    AuditLog.prototype.initFilters = function() {
        // Auto-submit form when filters change
        $('#filter-user, #filter-action, #filter-per-page').on('change', function() {
            $('#audit-filters').submit();
        });

        // Clear filters functionality
        $('.btn-outline-secondary').on('click', function(e) {
            e.preventDefault();
            window.location.href = 'audit.php';
        });

        // Add loading state to filter button
        $('#audit-filters').on('submit', function(e) {
            const submitBtn = $(this).find('button[type="submit"]');
            const originalText = submitBtn.html();
            submitBtn.html('<i class="ri-loader-2-line spinner-border spinner-border-sm"></i> Filtrando...');
            submitBtn.prop('disabled', true);

            // Ensure date values are in the correct format before submission
            const startDateInput = document.getElementById('filter-date-start');
            const endDateInput = document.getElementById('filter-date-end');

            if (startDateInput && startDateInput._flatpickr) {
                const startDate = startDateInput._flatpickr.selectedDates[0];
                if (startDate) {
                    const formattedDate = startDate.getDate().toString().padStart(2, '0') + '-' +
                                        (startDate.getMonth() + 1).toString().padStart(2, '0') + '-' +
                                        startDate.getFullYear();
                    startDateInput.value = formattedDate;
                }
            }

            if (endDateInput && endDateInput._flatpickr) {
                const endDate = endDateInput._flatpickr.selectedDates[0];
                if (endDate) {
                    const formattedDate = endDate.getDate().toString().padStart(2, '0') + '-' +
                                        (endDate.getMonth() + 1).toString().padStart(2, '0') + '-' +
                                        endDate.getFullYear();
                    endDateInput.value = formattedDate;
                }
            }
        });
    };

    AuditLog.prototype.initTooltips = function() {
        // Initialize tooltips for action badges
        $('[data-bs-toggle="tooltip"]').tooltip();

        // Add tooltips to IP addresses
        $('code').each(function() {
            $(this).attr('title', 'Endereço IP do utilizador');
            $(this).tooltip();
        });
    };

    AuditLog.prototype.initTableEnhancements = function() {
        // Add hover effects and click handlers
        $('#audit-table tbody tr').hover(
            function() {
                $(this).addClass('table-active');
            },
            function() {
                $(this).removeClass('table-active');
            }
        );

        // Add copy to clipboard for IP addresses
        $('code').on('click', function() {
            const text = $(this).text();
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(function() {
                    // Show success message
                    const $code = $(this);
                    const originalTitle = $code.attr('title');
                    $code.attr('title', 'IP copiado!').tooltip('show');
                    setTimeout(function() {
                        $code.attr('title', originalTitle);
                    }, 2000);
                });
            }
        });
    };

    AuditLog.prototype.init = function() {
        this.initDatePickers();
        this.initDetailsModal();
        this.initFilters();
        this.initTooltips();
        this.initTableEnhancements();

        console.log('Audit Log initialized successfully');
    };

    // Initialize when document is ready
    $(document).ready(function() {
        new AuditLog();
    });

})(window.jQuery);
