<?php
/**
 * AJAX Maintenance Handler
 * Santa Casa da Misericórdia da Covilhã
 */

// Inicializar configuração
require_once '../config/config.php';

// Verificar se é uma requisição AJAX
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
    http_response_code(403);
    exit('Access denied');
}

// Inicializar autenticação
$auth = new Auth();

// Verificar se o utilizador está autenticado
if (!$auth->isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Não autenticado']);
    exit();
}

// Verificar permissões
if (!$auth->hasPermission('settings.edit')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Sem permissões']);
    exit();
}

// Obter dados JSON
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Dados inválidos']);
    exit();
}

// Verificar CSRF token
if (!verifyCSRFToken($input['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Token CSRF inválido']);
    exit();
}

$action = $input['action'] ?? '';

try {
    $systemMaintenance = new SystemMaintenance();
    $currentUser = $auth->getCurrentUser();
    
    switch ($action) {
        case 'clear_cache':
            $result = $systemMaintenance->clearCache();
            if ($result['success']) {
                // Log da ação
                if (class_exists('AuditLog')) {
                    $auditLog = new AuditLog();
                    $auditLog->log(
                        $currentUser['id'],
                        'maintenance_clear_cache',
                        'system',
                        null,
                        null,
                        ['files_deleted' => $result['files_deleted']]
                    );
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => "Cache limpa com sucesso. {$result['files_deleted']} ficheiros removidos."
                ]);
            } else {
                throw new Exception($result['error']);
            }
            break;
            
        case 'optimize_db':
            $result = $systemMaintenance->optimizeDatabase();
            if ($result['success']) {
                // Log da ação
                if (class_exists('AuditLog')) {
                    $auditLog = new AuditLog();
                    $auditLog->log(
                        $currentUser['id'],
                        'maintenance_optimize_db',
                        'system',
                        null,
                        null,
                        ['tables_optimized' => $result['tables_optimized']]
                    );
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => "Base de dados otimizada com sucesso. {$result['tables_optimized']} tabelas otimizadas."
                ]);
            } else {
                throw new Exception($result['error']);
            }
            break;
            
        case 'clean_logs':
            $result = $systemMaintenance->cleanOldLogs(90); // 90 days
            if ($result['success']) {
                // Log da ação
                if (class_exists('AuditLog')) {
                    $auditLog = new AuditLog();
                    $auditLog->log(
                        $currentUser['id'],
                        'maintenance_clean_logs',
                        'system',
                        null,
                        null,
                        ['records_deleted' => $result['records_deleted']]
                    );
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => "Logs antigos limpos com sucesso. {$result['records_deleted']} registos removidos."
                ]);
            } else {
                throw new Exception($result['error']);
            }
            break;
            
        case 'enable_maintenance':
            $message = $input['message'] ?? 'O sistema está temporariamente em manutenção. Tente novamente mais tarde.';
            if ($systemMaintenance->enableMaintenanceMode($message)) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Modo de manutenção ativado com sucesso.'
                ]);
            } else {
                throw new Exception('Erro ao ativar modo de manutenção');
            }
            break;
            
        case 'disable_maintenance':
            if ($systemMaintenance->disableMaintenanceMode()) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Modo de manutenção desativado com sucesso.'
                ]);
            } else {
                throw new Exception('Erro ao desativar modo de manutenção');
            }
            break;
            
        default:
            throw new Exception('Ação inválida');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
