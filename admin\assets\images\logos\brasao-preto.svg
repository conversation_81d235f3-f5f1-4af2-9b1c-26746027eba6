<?xml version="1.0" encoding="UTF-8"?>
<svg id="Camada_2" data-name="Camada 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 159.07 218.74">
  <defs>
    <style>
      .cls-1 {
        stroke-width: .38px;
      }

      .cls-1, .cls-2, .cls-3, .cls-4, .cls-5, .cls-6, .cls-7, .cls-8, .cls-9, .cls-10, .cls-11, .cls-12, .cls-13, .cls-14, .cls-15, .cls-16, .cls-17, .cls-18, .cls-19, .cls-20, .cls-21 {
        fill: none;
        stroke: #000;
      }

      .cls-1, .cls-2, .cls-3, .cls-4, .cls-5, .cls-6, .cls-7, .cls-8, .cls-10, .cls-11, .cls-12, .cls-13, .cls-14, .cls-15, .cls-16, .cls-17, .cls-18, .cls-19, .cls-20, .cls-21 {
        stroke-miterlimit: 10;
      }

      .cls-2 {
        stroke-width: .5px;
      }

      .cls-3 {
        stroke-width: .41px;
      }

      .cls-4 {
        stroke-width: .38px;
      }

      .cls-5 {
        stroke-width: .55px;
      }

      .cls-6, .cls-7, .cls-9, .cls-12, .cls-17 {
        stroke-linecap: round;
      }

      .cls-6, .cls-19 {
        stroke-width: .53px;
      }

      .cls-7 {
        stroke-width: .4px;
      }

      .cls-8 {
        stroke-width: .48px;
      }

      .cls-9 {
        stroke-linejoin: bevel;
      }

      .cls-9, .cls-12, .cls-15 {
        stroke-width: .25px;
      }

      .cls-10 {
        stroke-width: .5px;
      }

      .cls-11 {
        stroke-width: .45px;
      }

      .cls-22 {
        font-size: 4.82px;
      }

      .cls-22, .cls-23, .cls-24, .cls-25, .cls-26, .cls-27 {
        font-family: Faustina-ExtraBold, Faustina;
        font-weight: 700;
      }

      .cls-13 {
        stroke-width: .39px;
      }

      .cls-23 {
        font-size: 15.2px;
      }

      .cls-24 {
        font-size: 6.6px;
        letter-spacing: .07em;
      }

      .cls-14 {
        stroke-width: .44px;
      }

      .cls-16 {
        stroke-width: .42px;
      }

      .cls-25 {
        font-size: 7.14px;
        letter-spacing: .07em;
      }

      .cls-26 {
        font-size: 8.17px;
        letter-spacing: .07em;
      }

      .cls-17 {
        stroke-width: .5px;
      }

      .cls-18 {
        stroke-width: .46px;
      }

      .cls-27 {
        font-size: 15px;
      }

      .cls-20 {
        stroke-width: .5px;
      }

      .cls-21 {
        stroke-width: 2.09px;
      }
    </style>
  </defs>
  <g id="Camada_1-2" data-name="Camada 1">
    <g>
      <g>
        <path class="cls-11" d="M77.33,45.84v.03s-.01-.02-.01-.03h.01Z"/>
        <path class="cls-18" d="M77.15,38.4c-.1-.14-.19-.29-.26-.45-.42,0-.83-.01-1.24-.03-.45.43-1.07.68-1.71.68-.73,0-1.26-.33-1.57-.86-1.47-.11-2.86-.25-4.16-.42-.31.41-.58.85-.81,1.28,3.38.59,7.31.94,11.53.95-.73-.14-1.37-.56-1.78-1.15ZM77.15,38.4c-.1-.14-.19-.29-.26-.45-.42,0-.83-.01-1.24-.03-.45.43-1.07.68-1.71.68-.73,0-1.26-.33-1.57-.86-1.47-.11-2.86-.25-4.16-.42-.31.41-.58.85-.81,1.28,3.38.59,7.31.94,11.53.95-.73-.14-1.37-.56-1.78-1.15Z"/>
        <path class="cls-18" d="M77.33,45.87h0c.01,1.15-.43,3.57-2.63,4.81-.5.29-1.09.51-1.79.64t-.02.01c-.15.04-.32.09-.49.13-1.19.28-2.24.23-3.13,0-2.02-.5-3.23-1.85-3.39-2.04l-.02-.02-.25-.21-2.47-2.03c-.7-.43-1.48-.71-2.45-.71-1.63,0-3.55.15-5.3-.97-.95-.6-1.87-1.79-2.12-3.02-.18-.91.01-1.84.84-2.57.48-.43,1.09-.68,1.77-.68,1.1,0,2.06.8,2.25,1.82.18,2.9,2.23,3.29,3.53,2.96,1.13-.29,1.83-3.12,3.23-5.9.2-.4.42-.81.66-1.2.07-.12.14-.25.22-.37.82-1.3,1.63-2.21,2.4-2.83,1.12-.89,2.18-1.18,3.2-1.18,5.31,0,4.93,3.99,4.93,3.99,0,.56-.25,1.06-.65,1.42-.45.43-1.07.68-1.71.68-.73,0-1.26-.33-1.57-.86-.2-.34-.3-.77-.3-1.24,0,0,.32-1.29-1.16-1.22-.89.05-1.9.95-2.7,2.04-.31.41-.58.85-.81,1.28-.46.84-.74,1.65-.74,2.18,0,1.19,2.92,1.72,2.92,1.72,0,0-2.69.85-2.77,2.98-.05,1.44,1.75,3.73,4.41,3.44,2.59-.29,1.95-2.47,1.84-3.44-.14-1.19.98-2.17,2.18-2.17.97,0,1.54.56,1.83,1.28,0,.05,0,.11,0,.17,0,.39.09.76.26,1.08,0,.01.01.02.01.03Z"/>
        <path class="cls-18" d="M82,44.76c0,.39-.09.76-.26,1.08h-.01v.04c-.4.76-1.2,1.28-2.12,1.28h-.16c-.92,0-1.72-.52-2.12-1.28v-.04h-.01c-.17-.32-.26-.69-.26-1.08,0-.06,0-.12,0-.17.09-1.24,1.12-2.22,2.38-2.22h.16c1.26,0,2.29.98,2.38,2.22.01.05.01.11.01.17Z"/>
        <path class="cls-18" d="M77.33,45.84v.03s-.01-.02-.01-.03h.01Z"/>
        <rect class="cls-4" x="56.24" y="25.51" width="3.47" height="3.47" transform="translate(11.8 -17.16) rotate(18.71)"/>
        <rect class="cls-10" x="65.92" y="21.26" width="4.5" height="4.5" transform="translate(24.54 -33.91) rotate(33.74)"/>
        <path class="cls-8" d="M79.53,9.43v4.52h-.65c-9.49,0-18.25,3.14-25.29,8.44-.18.13-.36.27-.53.4-.2.21-.35.46-.44.74l-.1-.02-2.43-5.41s3.58.84,5.23,0c1.64-.85,2.65-3.64,2.65-3.64,0,0,3.25,1.35,5.69.3s4.51-3.29,4.51-3.29c0,0,2.56,1.65,5.1,1.3,2.54-.35,6.26-3.34,6.26-3.34Z"/>
        <polygon class="cls-14" points="50.09 18.1 47.04 11.89 52.08 12.97 50.09 18.1"/>
        <rect class="cls-1" x="54.72" y="8.74" width="4.77" height="4.77" transform="translate(13.21 -27.06) rotate(30)"/>
        <rect class="cls-3" x="65.34" y="5.03" width="5.14" height="5.14" transform="translate(21.63 -42.68) rotate(40.98)"/>
        <rect class="cls-20" x="76.4" y="1.65" width="6.26" height="6.26" transform="translate(19.91 57.64) rotate(-45)"/>
        <path class="cls-20" d="M15.34,59.35c1.82-4.14,5.67-5.92,8.74-5.23s4.11,3.54,4.11,6.52c0,2.69-4.76,7.24-8.22,2.39"/>
        <path class="cls-20" d="M74.7,50.68c-.5.29-1.09.51-1.79.64t-.02.01c-.15.04-.32.09-.49.13-1.19.28-2.24.23-3.13,0l-4.99,6.45.03.04c.29.03.59.1.89.2,2.23.73,3.59,2.96,3.59,5.47.11-.11,1.01-1.04,3.16-3.47,2.29-2.59,3.83-5.93,3.78-7.17-.03-.76-.28-1.68-1.03-2.3ZM77.32,45.84s.01.02.01.03v-.03h-.01Z"/>
        <path class="cls-20" d="M79.53,170.44v32.56c-1.08.02-2.05.51-2.7,1.29l-.03-.11-.27-.93-2.19-12.35s-9.36,10.46-13.74,11.15c-4.39.7-19.12-13.14-19.12-13.14,0,0,8.16,1.63,10.55-3.55,2.39-5.18-1.25-11.99-5.83-13.89-4.58-1.89-11.45-2.78-17.87,2.9,0,0-25.29-31.6-27.83-64.52-2.54-32.92,14.83-50.5,14.83-50.5,0,0-.37,10.02,6.43,10.56,8.61.67,19.14-8.82,19.14-8.82,0,0,12.32-12.25,22.24-13.93l2.47,2.03.25.21.02.02c.16.19,1.37,1.54,3.39,2.04l-4.99,6.45.03.04s-.05-.01-.07-.01c-.05-.01-.1-.01-.14-.02-.57.04-1.12.09-1.66.15-26.94,2.99-27.98,34.17-28.42,40.96-.79,12.32,1.84,26.67,8.55,36.35,14.21,20.52,36.96,35.06,36.96,35.06Z"/>
        <path class="cls-20" d="M79.53,47.16v118.7s-26.68-17.91-36.76-40.24c-7.82-17.31-3.59-37.74.5-48.99,3.05-8.42,10.99-12.71,14.8-14.34-.02.04-.04.09-.05.13-1.09,3.31.35,6.76,3.22,7.71s6.09-.96,7.18-4.27c.25-.75.37-1.51.37-2.24.11-.11,1.01-1.04,3.16-3.47,2.29-2.59,3.83-5.93,3.78-7.17-.03-.76-.28-1.68-1.03-2.3,2.2-1.24,2.64-3.66,2.63-4.8.4.76,1.2,1.28,2.12,1.28h.08Z"/>
        <path class="cls-20" d="M87.81,218.44c-4.02.36-6.92-1.5-8.28-4.88-1.36,3.38-4.26,5.24-8.28,4.88-6.69-.61-8.54-4.81-8.16-8.17.23-2.13,2.68-4.04,4.68-2.29,2.04,1.79,0,4.08,0,4.08,0,0-.05,2.99,4.68,2.29,2.63-.39,4.17-3.5,4.73-4.89.02-.03.03-.06.03-.09.38.34.83.59,1.32.74.08.02.15.05.23.06.11.03.21.04.32.06.08.01.16.02.24.03.07,0,.14.01.21.01s.14-.01.21-.01c.08-.01.16-.02.24-.03.11-.02.21-.03.32-.06.08-.01.15-.04.23-.06.49-.15.94-.4,1.32-.74,0,.03.01.06.03.09.56,1.39,2.1,4.5,4.73,4.89,4.73.7,4.68-2.29,4.68-2.29,0,0-2.04-2.29,0-4.08,2-1.75,4.45.16,4.68,2.29.38,3.36-1.47,7.56-8.16,8.17Z"/>
        <path class="cls-20" d="M64.28,57.91l-.04.03s-.1-.01-.14-.02c.06,0,.12-.01.18-.01Z"/>
        <path class="cls-19" d="M79.53,165.86v4.58s-22.75-14.54-36.96-35.06c-6.71-9.68-9.34-24.03-8.55-36.35.44-6.79,1.48-37.97,28.42-40.96.54-.06,1.09-.11,1.66-.15.04,0,.09,0,.14.02.02,0,.04,0,.07.01.29.03.59.1.89.2,2.23.73,3.59,2.96,3.59,5.47,0,.73-.12,1.49-.37,2.24-1.09,3.31-4.31,5.22-7.18,4.27s-4.31-4.4-3.22-7.71c.01-.04.03-.09.05-.13-3.81,1.63-11.75,5.92-14.8,14.34-4.09,11.25-8.32,31.68-.5,48.99,10.08,22.33,36.76,40.24,36.76,40.24Z"/>
        <path class="cls-19" d="M64.1,57.92c-.57.04-1.12.09-1.66.15.54-.14,1.1-.19,1.66-.15Z"/>
        <path class="cls-19" d="M75.73,52.98c.05,1.24-1.49,4.58-3.78,7.17-2.15,2.43-3.05,3.36-3.16,3.47,0-2.51-1.36-4.74-3.59-5.47-.3-.1-.6-.17-.89-.2l-.03-.04,4.99-6.45c.89.23,1.94.28,3.13,0,.17-.04.34-.09.49-.13t.02-.01c.7-.13,1.29-.35,1.79-.64.75.62,1,1.54,1.03,2.3Z"/>
        <circle cx="39.83" cy="181.63" r="3"/>
        <path class="cls-19" d="M41.48,188.91s-2.82-1.68-3.95-5.87"/>
        <line class="cls-2" x1="59.84" y1="35.15" x2="59.84" y2="31.78"/>
        <line class="cls-2" x1="61.29" y1="35.78" x2="61.29" y2="31.18"/>
        <polyline class="cls-2" points="63.01 30.55 63.01 30.61 63.01 36.31"/>
        <line class="cls-2" x1="65.74" y1="36.52" x2="65.74" y2="29.84"/>
        <line class="cls-13" x1="68.17" y1="33.7" x2="68.17" y2="29.64"/>
        <line class="cls-13" x1="74.27" y1="33.03" x2="74.27" y2="28.97"/>
        <path class="cls-14" d="M82.43,36.78c0,.42-.09.82-.26,1.17-.07.16-.16.31-.26.45-.41.59-1.05,1.01-1.78,1.15h-.12c-.16,0-.32,0-.48-.01-.16.01-.32.01-.48.01h-.12c-.73-.14-1.37-.56-1.78-1.15-.1-.14-.19-.29-.26-.45-.17-.35-.26-.75-.26-1.17,0-1.55,1.27-2.82,2.82-2.82h.16c1.55,0,2.82,1.27,2.82,2.82Z"/>
        <path class="cls-11" d="M81.74,45.84s-.01.02-.01.03v-.03h.01Z"/>
        <path class="cls-18" d="M106.54,24.14c0,1.08-.88,1.96-1.96,1.96-.2,0-.39-.03-.57-.09h0c-.3-.22-.61-.42-.92-.62,0,0-.01,0-.01-.01-6.59-4.27-14.45-6.76-22.89-6.76-.22,0-.43,0-.65.01-.22-.01-.43-.01-.65-.01-8.44,0-16.3,2.49-22.89,6.76,0,.01-.01.01-.01.01-.31.2-.62.4-.92.62h-.01c-.18.06-.37.09-.57.09-1.08,0-1.96-.88-1.96-1.96,0-.21.03-.42.1-.61.09-.28.24-.53.44-.74.17-.13.35-.27.53-.4,7.04-5.3,15.8-8.44,25.29-8.44.2,0,.41,0,.61.01h.08c.2-.01.41-.01.61-.01,9.49,0,18.25,3.14,25.29,8.44.18.13.36.27.53.4.2.21.35.46.44.74.07.19.1.4.1.61Z"/>
        <path class="cls-18" d="M90.85,37.32c-1.3.17-2.69.31-4.16.42-.31.53-.84.86-1.57.86-.64,0-1.26-.25-1.71-.68-.41.02-.82.03-1.24.03-.07.16-.16.31-.26.45-.41.59-1.05,1.01-1.78,1.15,4.22-.01,8.15-.36,11.53-.95-.23-.43-.5-.87-.81-1.28ZM90.85,37.32c-1.3.17-2.69.31-4.16.42-.31.53-.84.86-1.57.86-.64,0-1.26-.25-1.71-.68-.41.02-.82.03-1.24.03-.07.16-.16.31-.26.45-.41.59-1.05,1.01-1.78,1.15,4.22-.01,8.15-.36,11.53-.95-.23-.43-.5-.87-.81-1.28Z"/>
        <path class="cls-18" d="M102.75,32.87c0,2.12-3.35,4-8.58,5.22-.2-.4-.42-.81-.66-1.2.92-.18,1.77-.37,2.54-.57h0c.63-.17,1.21-.35,1.72-.53h0c.56-.21,1.04-.42,1.45-.64.97-.52,1.5-1.09,1.5-1.69s-.53-1.17-1.5-1.69c-.79-.42-1.86-.81-3.17-1.16-.82-.22-1.74-.42-2.73-.61-2.43-.44-5.34-.77-8.53-.93-1.53-.08-3.13-.12-4.77-.12h-.98c-1.64,0-3.24.04-4.77.12-3.19.16-6.1.49-8.53.93-.99.19-1.91.39-2.73.61-1.31.35-2.38.74-3.17,1.16-.97.52-1.5,1.09-1.5,1.69s.53,1.17,1.5,1.69c.41.22.89.43,1.45.63h0c.51.19,1.09.37,1.72.53h0c.77.21,1.62.4,2.54.58-.24.39-.46.8-.66,1.2-5.23-1.22-8.58-3.1-8.58-5.22,0-3.7,10.18-6.69,22.74-6.69h.96c12.56,0,22.74,2.99,22.74,6.69Z"/>
        <path class="cls-18" d="M105.79,42.46c-.25,1.23-1.17,2.42-2.12,3.02-1.75,1.12-3.67.97-5.3.97-.97,0-1.75.28-2.45.71l-2.47,2.03-.25.21s0,.01-.02.02c-.16.19-1.37,1.54-3.39,2.04-.89.23-1.94.28-3.13,0-.17-.04-.34-.09-.49-.13t-.02-.01c-.7-.13-1.29-.35-1.79-.64-2.2-1.24-2.64-3.66-2.63-4.8h0s.01-.03.01-.04c.17-.32.26-.69.26-1.08,0-.06,0-.12-.01-.17.29-.72.86-1.28,1.83-1.28,1.2,0,2.32.98,2.18,2.17-.11.97-.75,3.15,1.84,3.44,2.66.29,4.46-2,4.41-3.44-.08-2.13-2.77-2.98-2.77-2.98,0,0,2.92-.53,2.92-1.72,0-.53-.28-1.34-.74-2.18-.23-.43-.5-.87-.81-1.28-.8-1.09-1.81-1.99-2.7-2.04-1.48-.07-1.16,1.22-1.16,1.22,0,.47-.1.9-.3,1.24-.31.53-.84.86-1.57.86-.64,0-1.26-.25-1.71-.68-.4-.36-.65-.86-.65-1.42,0,0-.38-3.99,4.93-3.99,1.02,0,2.08.29,3.2,1.18.77.62,1.58,1.53,2.4,2.83.08.12.15.25.22.37.24.39.46.8.66,1.2,1.4,2.78,2.1,5.61,3.23,5.9,1.3.33,3.35-.06,3.53-2.96.19-1.02,1.15-1.82,2.25-1.82.68,0,1.29.25,1.77.68.83.73,1.02,1.66.84,2.57Z"/>
        <path class="cls-18" d="M81.74,45.84s-.01.02-.01.03v-.03h.01Z"/>
        <rect class="cls-4" x="99.36" y="25.51" width="3.47" height="3.47" transform="translate(205.58 20.6) rotate(161.29)"/>
        <rect class="cls-10" x="88.64" y="21.26" width="4.5" height="4.5" transform="translate(179.53 -7.43) rotate(146.26)"/>
        <rect class="cls-5" x="77.06" y="19.84" width="4.94" height="4.94" transform="translate(7.58 62.87) rotate(-45.08)"/>
        <path class="cls-8" d="M108.97,18.1l-2.43,5.41-.1.02c-.09-.28-.24-.53-.44-.74-.17-.13-.35-.27-.53-.4-7.04-5.3-15.8-8.44-25.29-8.44-.2,0-.41,0-.61.01h-.04v-4.53s3.72,2.99,6.26,3.34,5.1-1.3,5.1-1.3c0,0,2.07,2.24,4.51,3.29s5.69-.3,5.69-.3c0,0,1.01,2.79,2.65,3.64,1.65.84,5.23,0,5.23,0Z"/>
        <polygon class="cls-14" points="108.97 18.1 112.02 11.89 106.98 12.97 108.97 18.1"/>
        <rect class="cls-1" x="99.58" y="8.74" width="4.77" height="4.77" transform="translate(195.82 -30.22) rotate(150)"/>
        <rect class="cls-3" x="88.58" y="5.03" width="5.14" height="5.14" transform="translate(164.95 -46.44) rotate(139.02)"/>
        <path class="cls-20" d="M143.73,59.35c-1.82-4.14-5.67-5.92-8.74-5.23s-4.11,3.54-4.11,6.52c0,2.69,4.76,7.24,8.22,2.39"/>
        <path class="cls-20" d="M89.79,51.46c-.89.23-1.94.28-3.13,0-.17-.04-.34-.09-.49-.13t-.02-.01c-.7-.13-1.29-.35-1.79-.64-.75.62-1,1.54-1.03,2.3-.05,1.24,1.49,4.58,3.78,7.17,2.15,2.43,3.05,3.36,3.16,3.47,0-2.51,1.36-4.74,3.59-5.47.3-.1.6-.17.89-.2l.03-.04-4.99-6.45ZM81.73,45.84v.03s.01-.02.01-.03h-.01Z"/>
        <path class="cls-20" d="M158.56,109.85c-2.54,32.92-27.83,64.52-27.83,64.52-6.42-5.68-13.29-4.79-17.87-2.9-4.58,1.9-8.75,8.71-6.36,13.89,2.39,5.18,11.08,3.55,11.08,3.55,0,0-14.73,13.84-19.12,13.14-4.38-.69-13.74-11.15-13.74-11.15l-2.19,12.35-.27.93-.03.11c-.65-.78-1.62-1.27-2.7-1.29v-32.56s22.75-14.54,36.96-35.06c6.71-9.68,9.34-24.03,8.55-36.35-.44-6.79-1.48-37.97-28.42-40.96-.54-.06-1.09-.11-1.66-.15-.04,0-.09,0-.14.02-.02,0-.04,0-.07.01l.03-.04-4.99-6.45c2.02-.5,3.23-1.85,3.39-2.04.01-.01.02-.02.02-.02l.25-.21,2.47-2.03c9.92,1.68,22.24,13.93,22.24,13.93,0,0,10.53,9.49,19.14,8.82,6.8-.54,6.43-10.56,6.43-10.56,0,0,17.37,17.58,14.83,50.5Z"/>
        <path class="cls-20" d="M116.29,125.62c-10.08,22.33-36.76,40.24-36.76,40.24V47.16h.08c.92,0,1.72-.52,2.12-1.28-.01,1.14.43,3.56,2.63,4.8-.75.62-1,1.54-1.03,2.3-.05,1.24,1.49,4.58,3.78,7.17,2.15,2.43,3.05,3.36,3.16,3.47,0,.73.12,1.49.37,2.24,1.09,3.31,4.31,5.22,7.18,4.27,2.87-.95,4.31-4.4,3.22-7.71-.01-.04-.03-.09-.05-.13,3.81,1.63,11.75,5.92,14.8,14.34,4.09,11.25,8.32,31.68.5,48.99Z"/>
        <path class="cls-20" d="M94.78,57.91l.04.03s.1-.01.14-.02c-.06,0-.12-.01-.18-.01Z"/>
        <path class="cls-19" d="M79.53,165.86v4.58s22.75-14.54,36.96-35.06c6.71-9.68,9.34-24.03,8.55-36.35-.44-6.79-1.48-37.97-28.42-40.96-.54-.06-1.09-.11-1.66-.15-.04,0-.09,0-.14.02-.02,0-.04,0-.07.01-.29.03-.59.1-.89.2-2.23.73-3.59,2.96-3.59,5.47,0,.73.12,1.49.37,2.24,1.09,3.31,4.31,5.22,7.18,4.27s4.31-4.4,3.22-7.71c-.01-.04-.03-.09-.05-.13,3.81,1.63,11.75,5.92,14.8,14.34,4.09,11.25,8.32,31.68.5,48.99-10.08,22.33-36.76,40.24-36.76,40.24Z"/>
        <path class="cls-19" d="M94.96,57.92c.57.04,1.12.09,1.66.15-.54-.14-1.1-.19-1.66-.15Z"/>
        <path class="cls-19" d="M116.49,135.38c-14.21,20.52-36.96,35.06-36.96,35.06,0,0-22.75-14.54-36.96-35.06-6.71-9.68-9.34-24.03-8.55-36.35.44-6.79,1.48-37.97,28.42-40.96.54-.06,1.09-.11,1.66-.15.04,0,.09,0,.14.02.02,0,.04,0,.07.01.29.03.59.1.89.2,2.23.73,3.59,2.96,3.59,5.47,0,.73-.12,1.49-.37,2.24-1.09,3.31-4.31,5.22-7.18,4.27s-4.31-4.4-3.22-7.71c.01-.04.03-.09.05-.13-3.81,1.63-11.75,5.92-14.8,14.34-4.09,11.25-8.32,31.68-.5,48.99,10.08,22.33,36.76,40.24,36.76,40.24,0,0,26.68-17.91,36.76-40.24,7.82-17.31,3.59-37.74-.5-48.99-3.05-8.42-10.99-12.71-14.8-14.34.02.04.04.09.05.13,1.09,3.31-.35,6.76-3.22,7.71-2.87.95-6.09-.96-7.18-4.27-.25-.75-.37-1.51-.37-2.24,0-2.51,1.36-4.74,3.59-5.47.3-.1.6-.17.89-.2.03-.01.05-.01.07-.01.05-.01.1-.01.14-.02.57.04,1.12.09,1.66.15,26.94,2.99,27.98,34.17,28.42,40.96.79,12.32-1.84,26.67-8.55,36.35Z"/>
        <path class="cls-19" d="M94.78,57.91l-.03.04c-.29.03-.59.1-.89.2-2.23.73-3.59,2.96-3.59,5.47-.11-.11-1.01-1.04-3.16-3.47-2.29-2.59-3.83-5.93-3.78-7.17.03-.76.28-1.68,1.03-2.3.5.29,1.09.51,1.79.64t.02.01c.15.04.32.09.49.13,1.19.28,2.24.23,3.13,0l4.99,6.45Z"/>
        <circle cx="119.24" cy="181.63" r="3"/>
        <path class="cls-6" d="M117.59,188.91c.12-.28,3.67-2.3,4.27-6.47"/>
        <line class="cls-2" x1="99.22" y1="35.15" x2="99.22" y2="31.78"/>
        <line class="cls-2" x1="97.77" y1="35.78" x2="97.77" y2="31.18"/>
        <polyline class="cls-2" points="96.05 30.55 96.05 30.61 96.05 36.31"/>
        <line class="cls-2" x1="93.33" y1="36.52" x2="93.33" y2="29.84"/>
        <line class="cls-13" x1="90.89" y1="33.7" x2="90.89" y2="29.64"/>
        <line class="cls-13" x1="84.79" y1="33.03" x2="84.79" y2="28.97"/>
        <circle class="cls-20" cx="79.53" cy="206.58" r="3.5"/>
        <polygon points="69.73 85.84 63.27 85.84 63.27 77.01 58.27 77.01 58.27 85.84 51.5 85.84 51.5 89.73 58.27 89.73 58.27 105.87 63.27 105.87 63.27 89.73 69.73 89.73 69.73 85.84"/>
        <path class="cls-2" d="M90.28,80.08l15.54,2.99s4.41,8.45,2.84,24.95c-.95,9.91-3.19,14.94-6.3,20.22-2.4,4.07-12.08,14.54-12.08,14.54v-62.69Z"/>
        <path class="cls-16" d="M92.24,82.84l12.38,2.65s3.52,7.5,2.26,22.13c-.75,8.79-2.54,13.25-5.02,17.93-1.91,3.61-9.63,12.9-9.63,12.9v-55.6Z"/>
        <path d="M98,94.84h3.4v3.08c0,.94-.76,1.7-1.7,1.7h0c-.94,0-1.7-.76-1.7-1.7v-3.08h0Z"/>
        <path d="M98,103.4h3.4v3.08c0,.94-.76,1.7-1.7,1.7h0c-.94,0-1.7-.76-1.7-1.7v-3.08h0Z"/>
        <path d="M102.36,103.4h3.4v3.08c0,.94-.76,1.7-1.7,1.7h0c-.94,0-1.7-.76-1.7-1.7v-3.08h0Z"/>
        <path d="M93.56,103.4h3.4v3.08c0,.94-.76,1.7-1.7,1.7h0c-.94,0-1.7-.76-1.7-1.7v-3.08h0Z"/>
        <path d="M98,111.12h3.4v3.08c0,.94-.76,1.7-1.7,1.7h0c-.94,0-1.7-.76-1.7-1.7v-3.08h0Z"/>
        <polygon points="88.26 82.91 88.26 85 88.02 85.14 86.55 86.01 87.57 92.5 82.35 92.5 83.43 86.01 81.82 85.14 81.55 85 81.55 82.91 82.56 82.91 82.56 85 83.45 85 83.45 82.91 84.47 82.91 84.47 85 85.34 85 85.34 82.91 86.36 82.91 86.36 85 87.25 85 87.25 82.91 88.26 82.91"/>
        <polygon points="101.79 70.97 101.79 73.06 101.55 73.21 100.08 74.07 101.1 80.57 95.88 80.57 96.96 74.07 95.35 73.21 95.08 73.06 95.08 70.97 96.09 70.97 96.09 73.06 96.98 73.06 96.98 70.97 98 70.97 98 73.06 98.88 73.06 98.88 70.97 99.89 70.97 99.89 73.06 100.78 73.06 100.78 70.97 101.79 70.97"/>
        <polygon points="88.26 107.62 88.26 109.7 88.02 109.85 86.55 110.72 87.57 117.21 82.35 117.21 83.43 110.72 81.82 109.85 81.55 109.7 81.55 107.62 82.56 107.62 82.56 109.7 83.45 109.7 83.45 107.62 84.47 107.62 84.47 109.7 85.34 109.7 85.34 107.62 86.36 107.62 86.36 109.7 87.25 109.7 87.25 107.62 88.26 107.62"/>
        <polygon points="88.26 133.1 88.26 135.19 88.02 135.33 86.55 136.2 87.57 142.69 82.35 142.69 83.43 136.2 81.82 135.33 81.55 135.19 81.55 133.1 82.56 133.1 82.56 135.19 83.45 135.19 83.45 133.1 84.47 133.1 84.47 135.19 85.34 135.19 85.34 133.1 86.36 133.1 86.36 135.19 87.25 135.19 87.25 133.1 88.26 133.1"/>
        <polygon points="108.37 135.55 107.14 137.23 106.86 137.2 105.16 137.03 102.13 142.86 97.93 139.77 102.64 135.18 101.86 133.53 101.73 133.25 102.96 131.57 103.78 132.17 102.54 133.85 103.26 134.38 104.5 132.7 105.31 133.3 104.08 134.98 104.79 135.5 106.02 133.82 106.84 134.42 105.6 136.1 106.32 136.63 107.55 134.94 108.37 135.55"/>
        <polygon points="117.88 107.27 117.53 109.33 117.27 109.44 115.68 110.05 115.62 116.62 110.47 115.77 112.6 109.54 111.15 108.42 110.91 108.23 111.25 106.17 112.25 106.34 111.91 108.4 112.79 108.54 113.13 106.49 114.13 106.65 113.79 108.71 114.66 108.85 115 106.8 116 106.96 115.66 109.02 116.53 109.17 116.88 107.11 117.88 107.27"/>
        <polygon points="115.98 90.68 110.87 91.75 110.6 85.18 108.85 84.65 108.55 84.57 108.13 82.53 109.12 82.32 109.55 84.36 110.42 84.18 109.99 82.14 110.98 81.93 111.41 83.97 112.27 83.79 111.84 81.75 112.84 81.54 113.26 83.58 114.13 83.4 113.7 81.36 114.7 81.15 115.12 83.19 114.92 83.39 113.66 84.54 115.98 90.68"/>
        <path class="cls-21" d="M73.87,126.36c-.74,2.09-8.04,4.11-12.88,4.23-4.84.12-11.67-1.52-12.57-4.23-.89-2.71,2.39-6.81,1.72-8.01-.66-1.21-2.53-3.79-1.95-5.31.59-1.52,4.57-1.83,6.71-3.25,2.15-1.43,4.62-5.24,6.09-4.86,2.38.63,1.95,1.68,4.69,2.11,2.73.43,4.72.89,6.4,2.75,1.68,1.85.04,6.33-.16,8.48-.19,2.14,2.7,6,1.95,8.09Z"/>
        <path class="cls-2" d="M63.43,118.45c0,1.25-.7,2.33-1.73,2.88l-.34,1.76c-.08.41-.44.71-.86.7h-.7c-.4-.01-.75-.3-.82-.69l-.34-1.77c-1.03-.55-1.73-1.63-1.73-2.88,0-1.8,1.46-3.26,3.26-3.26s3.26,1.46,3.26,3.26Z"/>
        <ellipse cx="58.76" cy="118.73" rx=".66" ry="1.02" transform="translate(-66.74 76.32) rotate(-45)"/>
        <ellipse cx="61.53" cy="118.73" rx="1.02" ry=".66" transform="translate(-69.53 92.17) rotate(-51.15)"/>
        <path d="M57.91,116.11l-1.65-1.33c.13-.16.23-.37.23-.6,0-.53-.43-.96-.96-.96s-.96.43-.96.96c0,.1.03.2.06.29-.41.03-.74.37-.74.79,0,.44.36.8.8.8.35,0,.64-.22.75-.53l1.78,1.43.69-.86Z"/>
        <path d="M57.86,120.77l-1.74,1.71c-.17-.17-.39-.31-.65-.34-.6-.07-1.15.36-1.22.97s.36,1.15.97,1.22c.12.01.23,0,.34-.03-.01.47.32.89.8.95.51.06.96-.31,1.02-.81.05-.4-.17-.75-.51-.92l1.87-1.84-.89-.9Z"/>
        <path d="M62.5,116.11l1.65-1.33c-.13-.16-.23-.37-.23-.6,0-.53.43-.96.96-.96s.96.43.96.96c0,.1-.03.2-.06.29.41.03.74.37.74.79s-.36.8-.8.8c-.35,0-.64-.22-.75-.53l-1.78,1.43-.69-.86Z"/>
        <path d="M62.55,120.77l1.74,1.71c.17-.17.39-.31.65-.34.6-.07,1.15.36,1.22.97s-.36,1.15-.97,1.22c-.12.01-.23,0-.34-.03.01.47-.32.89-.8.95-.51.06-.96-.31-1.02-.81-.05-.4.17-.75.51-.92l-1.87-1.84.89-.9Z"/>
        <line class="cls-15" x1="115.17" y1="116.55" x2="115.17" y2="127.96"/>
        <line class="cls-15" x1="115.17" y1="75.09" x2="115.17" y2="88.52"/>
        <line class="cls-15" x1="115.17" y1="90.85" x2="115.17" y2="106.8"/>
        <line class="cls-9" x1="109.58" y1="67.69" x2="109.58" y2="83.82"/>
        <line class="cls-9" x1="109.58" y1="85.02" x2="109.58" y2="137.05"/>
        <line class="cls-12" x1="104.19" y1="143.84" x2="104.19" y2="138.9"/>
        <line class="cls-12" x1="98.49" y1="139.22" x2="98.49" y2="133.5"/>
        <line class="cls-12" x1="98.49" y1="149.94" x2="98.49" y2="140.19"/>
        <line class="cls-12" x1="93.42" y1="154.68" x2="93.42" y2="139.37"/>
        <line class="cls-12" x1="104.19" y1="133.1" x2="104.19" y2="124.91"/>
        <line class="cls-12" x1="104.19" y1="82.84" x2="104.19" y2="63.87"/>
        <line class="cls-12" x1="93.56" y1="80.46" x2="93.56" y2="69.59"/>
        <line class="cls-12" x1="88.58" y1="158.63" x2="88.58" y2="61.75"/>
        <line class="cls-12" x1="84.06" y1="162.56" x2="84.06" y2="142.86"/>
        <line class="cls-12" x1="84.06" y1="133.1" x2="84.06" y2="117.21"/>
        <line class="cls-12" x1="84.06" y1="107.57" x2="84.06" y2="92.61"/>
        <line class="cls-12" x1="84.06" y1="82.8" x2="84.06" y2="55.58"/>
        <line class="cls-12" x1="98.63" y1="81.42" x2="98.63" y2="80.57"/>
        <line class="cls-12" x1="98.63" y1="73.06" x2="98.63" y2="69.92"/>
        <line class="cls-12" x1="22.01" y1="158.63" x2="31.12" y2="172.31"/>
        <path class="cls-17" d="M59.75,198.65s-7.52-4.69-12.25-9.74"/>
        <path class="cls-17" d="M62.64,196.56s-4.83-2.69-9.46-7.07"/>
        <path class="cls-17" d="M57.46,187.9s5.08,4.85,7.37,5.88"/>
        <path class="cls-17" d="M92.96,193.78s4.63-2.48,7.97-5.79"/>
        <path class="cls-17" d="M105.21,189.49s-2.89,3.54-9.41,7.07"/>
        <path class="cls-17" d="M98.94,198.2s5.48-2.05,12.15-9.29"/>
        <path class="cls-17" d="M142.46,75.77s8.07,15.38,2.99,40.78-21.71,50.29-21.71,50.29"/>
        <path class="cls-17" d="M144.2,71.68s10.45,13.3,5.71,42.51c-4.22,26.01-23.26,54.1-23.26,54.1"/>
        <path class="cls-17" d="M146.1,67.6s12.26,16.39,7.71,45.62c-4.76,30.62-24,56.15-24,56.15"/>
        <path class="cls-17" d="M30.72,74.94s-1.27,2.12-1.74,4.53-.5,4.33-.5,4.33"/>
        <path class="cls-17" d="M33.51,75.14s-8.27,17.2-2.31,46.09c2.6,12.61,16.4,27.64,16.89,27.47"/>
        <path class="cls-17" d="M36.59,73.85s-9.11,15.96-3.25,44.87c3.88,19.17,20.55,33.12,20.55,33.12"/>
        <path class="cls-17" d="M121.94,74.4s2.71,3.78,3.62,7.62c1.08,4.56,1.08,8.47,1.08,8.47"/>
        <path class="cls-7" d="M124.76,74.7s2.17,3.03,2.91,6.11c.87,3.66.87,6.79.87,6.79"/>
        <path class="cls-12" d="M127.57,75.13s1.28,1.65,1.75,3.35c.56,2.02.62,3.78.62,3.78"/>
        <line class="cls-19" x1="49.61" y1="137.21" x2="79.53" y2="137.21"/>
        <line class="cls-19" x1="53.89" y1="142.77" x2="79.53" y2="142.77"/>
        <line class="cls-19" x1="58.14" y1="147.44" x2="79.53" y2="147.44"/>
        <line class="cls-19" x1="63.42" y1="152.71" x2="79.53" y2="152.71"/>
        <line class="cls-19" x1="69.32" y1="157.98" x2="79.53" y2="157.98"/>
        <line class="cls-19" x1="75.01" y1="162.56" x2="79.53" y2="162.56"/>
      </g>
      <text class="cls-27" transform="translate(14.99 80.75) rotate(101.68)"><tspan x="0" y="0">S</tspan></text>
      <text class="cls-27" transform="translate(13.08 90.3) rotate(93.7)"><tspan x="0" y="0">A</tspan></text>
      <text class="cls-27" transform="translate(12.42 101.99) rotate(85.12)"><tspan x="0" y="0">N</tspan></text>
      <text class="cls-27" transform="translate(13.69 114.51) rotate(76.81)"><tspan x="0" y="0">T</tspan></text>
      <text class="cls-27" transform="translate(15.97 124.1) rotate(68.86)"><tspan x="0" y="0">A</tspan></text>
      <text class="cls-27" transform="translate(20.31 134.6) rotate(63.16)"><tspan x="0" y="0"> </tspan></text>
      <text class="cls-27" transform="translate(21.92 137.99) rotate(57.94)"><tspan x="0" y="0">C</tspan></text>
      <text class="cls-27" transform="translate(27.6 146.8) rotate(51.5)"><tspan x="0" y="0">A</tspan></text>
      <text class="cls-27" transform="translate(34.65 155.55) rotate(47.16)"><tspan x="0" y="0">S</tspan></text>
      <text class="cls-27" transform="translate(41.06 162.46) rotate(42.04)"><tspan x="0" y="0">A</tspan></text>
      <text class="cls-27" transform="translate(49.51 169.84) rotate(37.8)"><tspan x="0" y="0"> </tspan></text>
      <text class="cls-27" transform="translate(52.39 172.24) rotate(32.6)"><tspan x="0" y="0">D</tspan></text>
      <text class="cls-27" transform="translate(63.08 178.93) rotate(22.14)"><tspan x="0" y="0">A</tspan></text>
      <text class="cls-23" transform="translate(87.11 182.95) rotate(-30.64)"><tspan x="0" y="0">M</tspan></text>
      <text class="cls-23" transform="translate(99.64 175.2) rotate(-36.36)"><tspan x="0" y="0">I</tspan></text>
      <text class="cls-23" transform="translate(105.22 171.1) rotate(-40.29)"><tspan x="0" y="0">S</tspan></text>
      <text class="cls-23" transform="translate(112.47 164.9) rotate(-44.95)"><tspan x="0" y="0">E</tspan></text>
      <text class="cls-23" transform="translate(119.67 157.65) rotate(-50)"><tspan x="0" y="0">R</tspan></text>
      <text class="cls-23" transform="translate(126.56 149.26) rotate(-54.3)"><tspan x="0" y="0">I</tspan></text>
      <text class="cls-23" transform="translate(130.75 143.56) rotate(-59.57)"><tspan x="0" y="0">C</tspan></text>
      <text class="cls-23" transform="translate(136.3 133.82) rotate(-70.34)"><tspan x="0" y="0">Ó</tspan></text>
      <text class="cls-23" transform="translate(140.34 121.83) rotate(-79.52)"><tspan x="0" y="0">R</tspan></text>
      <text class="cls-23" transform="translate(142.3 110.78) rotate(-87.09)"><tspan x="0" y="0">D</tspan></text>
      <text class="cls-23" transform="translate(142.73 98.52) rotate(-92.52)"><tspan x="0" y="0">I</tspan></text>
      <text class="cls-23" transform="translate(142.46 91.47) rotate(-97.57)"><tspan x="0" y="0">A</tspan></text>
      <text class="cls-26" transform="translate(41.51 99.68)"><tspan x="0" y="0">MI</tspan></text>
      <text class="cls-24" transform="translate(68.7 100.33)"><tspan x="0" y="0">Z</tspan></text>
      <text class="cls-25" transform="translate(71.53 92.19) scale(1.14 1)"><tspan x="0" y="0">A</tspan></text>
      <text class="cls-22" transform="translate(50.57 135.53) scale(1.14 1)"><tspan x="0" y="0">COVILHÃ</tspan></text>
    </g>
  </g>
</svg>