/*! Bootstrap 4 integration for DataTables' Responsive
 * © SpryMedia Ltd - datatables.net/license
 */
import jQuery from"jquery";import DataTable from"datatables.net-bs4";import Responsive from"datatables.net-responsive";let $=jQuery;var _display=DataTable.Responsive.display,_original=_display.modal,_modal=$('<div class="modal fade dtr-bs-modal" role="dialog"><div class="modal-dialog" role="document"><div class="modal-content"><div class="modal-header"><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button></div><div class="modal-body"/></div></div></div>');_display.modal=function(t){return function(a,d,e,o){if($.fn.modal){if(d){if(!$.contains(document,_modal[0])||a.index()!==_modal.data("dtr-row-idx"))return null;_modal.find("div.modal-body").empty().append(e())}else{var l,i;t&&t.header&&(i=(l=_modal.find("div.modal-header")).find("button").detach(),l.empty().append('<h4 class="modal-title">'+t.header(a)+"</h4>").append(i)),_modal.find("div.modal-body").empty().append(e()),_modal.data("dtr-row-idx",a.index()).one("hidden.bs.modal",o).appendTo("body").modal()}return!0}return _original(a,d,e,o)}};export default DataTable;