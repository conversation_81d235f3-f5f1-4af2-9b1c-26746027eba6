/**
 * JavaScript do Painel Administrativo
 * Santa Casa da Misericórdia da Covilhã
 */

// Configuração global
const AdminPanel = {
    config: {
        csrfToken: '',
        apiUrl: '/admin/api',
        language: 'pt'
    },
    
    // Inicialização
    init: function() {
        this.setupCSRF();
        this.setupEventListeners();
        this.setupFormValidation();
        this.setupPasswordStrength();
        this.setupUserDropdown();
        this.setupMobileMenu();
        this.setupNotificationSystem();

        // Only setup auto-logout if not on authentication pages
        if (!this.isAuthenticationPage()) {
            this.setupAutoLogout();
        }
    },

    // Check if current page is an authentication page
    isAuthenticationPage: function() {
        const authPages = ['login.php', 'forgot-password.php', 'reset-password.php', 'register.php'];
        const currentPage = window.location.pathname.split('/').pop();
        return authPages.includes(currentPage) || document.body.classList.contains('authentication-bg');
    },
    
    // Configurar token CSRF
    setupCSRF: function() {
        const token = document.querySelector('meta[name="csrf-token"]');
        if (token) {
            this.config.csrfToken = token.getAttribute('content');
        }
    },
    
    // Configurar event listeners
    setupEventListeners: function() {
        // Formulários AJAX
        document.querySelectorAll('form[data-ajax="true"]').forEach(form => {
            form.addEventListener('submit', this.handleAjaxForm.bind(this));
        });
        
        // Botões de confirmação
        document.querySelectorAll('[data-confirm]').forEach(button => {
            button.addEventListener('click', this.handleConfirmAction.bind(this));
        });
        
        // Auto-logout por inatividade (only if not on auth pages)
        if (!this.isAuthenticationPage()) {
            this.setupAutoLogout();
        }
    },

    // Setup notification system
    setupNotificationSystem: function() {
        // Create notification container if it doesn't exist
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'notification-container';
            document.body.appendChild(container);
        }

        // Create modal container for confirmations if it doesn't exist
        if (!document.getElementById('confirmation-modal')) {
            const modal = document.createElement('div');
            modal.id = 'confirmation-modal';
            modal.className = 'modal fade';
            modal.setAttribute('tabindex', '-1');
            modal.setAttribute('role', 'dialog');
            modal.innerHTML = `
                <div class="modal-dialog modal-sm modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Confirmação</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p class="mb-0" id="confirmation-message"></p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancelar</button>
                            <button type="button" class="btn btn-primary" id="confirmation-confirm">Confirmar</button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }
    },
    
    // Configurar validação de formulários
    setupFormValidation: function() {
        document.querySelectorAll('form[data-validate="true"]').forEach(form => {
            form.addEventListener('submit', this.validateForm.bind(this));
            
            // Validação em tempo real
            form.querySelectorAll('input, textarea, select').forEach(field => {
                field.addEventListener('blur', () => this.validateField(field));
                field.addEventListener('input', () => this.clearFieldError(field));
            });
        });
    },
    
    // Configurar indicador de força da password
    setupPasswordStrength: function() {
        document.querySelectorAll('input[type="password"][data-strength="true"]').forEach(input => {
            const container = document.createElement('div');
            container.className = 'password-strength';
            container.innerHTML = `
                <div class="password-strength-bar">
                    <div class="password-strength-fill"></div>
                </div>
                <small class="password-strength-text"></small>
            `;
            
            input.parentNode.insertBefore(container, input.nextSibling);
            
            input.addEventListener('input', () => {
                this.updatePasswordStrength(input, container);
            });
        });
    },
    
    // Configurar dropdown do utilizador e notificações
    setupUserDropdown: function() {
        // Bootstrap 5 automatically handles dropdowns with data-bs-toggle="dropdown"
        // No custom initialization needed
        console.log('Dropdown setup: Letting Bootstrap handle dropdowns automatically');
    },

    // Configurar menu mobile
    setupMobileMenu: function() {
        // Mobile menu functionality
        // The button-toggle-menu class is handled by the template
        // Add any custom mobile menu enhancements here if needed
        const toggleButton = document.querySelector('.button-toggle-menu');
        if (toggleButton) {
            // Mobile menu toggle button is available
        }
    },
    
    // Configurar auto-logout
    setupAutoLogout: function() {
        let warningTimeout;
        let logoutTimeout;
        const warningTime = 5 * 60 * 1000; // 5 minutos antes
        const logoutTime = 60 * 60 * 1000; // 1 hora total

        const resetTimer = () => {
            clearTimeout(warningTimeout);
            clearTimeout(logoutTimeout);

            // Timer para mostrar aviso
            warningTimeout = setTimeout(() => {
                this.showLogoutWarning();
            }, logoutTime - warningTime);

            // Timer para logout automático
            logoutTimeout = setTimeout(() => {
                this.forceLogout();
            }, logoutTime);
        };

        // Reset timer em atividade
        ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
            document.addEventListener(event, resetTimer, true);
        });

        resetTimer();
    },
    
    // Mostrar aviso de logout
    showLogoutWarning: function() {
        let warningTimeout;

        const modal = this.showConfirmation(
            'A sua sessão expirará em 5 minutos. Deseja continuar?',
            () => {
                // Renovar sessão
                clearTimeout(warningTimeout);
                this.renewSession();
            },
            () => {
                clearTimeout(warningTimeout);
                window.location.href = '/admin/logout';
            }
        );

        // Auto-logout após 5 minutos se não houver resposta
        warningTimeout = setTimeout(() => {
            // Fechar modal se ainda estiver aberto
            if (modal && modal.parentNode) {
                modal.remove();
            }
            this.forceLogout();
        }, 5 * 60 * 1000);
    },
    
    // Renovar sessão
    renewSession: function() {
        fetch('/admin/api/renew-session', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': this.config.csrfToken
            }
        }).then(response => {
            if (!response.ok) {
                this.forceLogout();
            } else {
                // Reiniciar timers após renovação bem-sucedida
                this.setupAutoLogout();
            }
        }).catch(() => {
            this.forceLogout();
        });
    },

    // Forçar logout
    forceLogout: function() {
        // Mostrar mensagem de sessão expirada
        this.showNotification('A sua sessão expirou. Será redirecionado para o login.', 'warning', 3000);

        // Redirecionar após 3 segundos
        setTimeout(() => {
            window.location.href = '/admin/logout';
        }, 3000);
    },
    
    // Processar formulários AJAX
    handleAjaxForm: function(e) {
        e.preventDefault();
        
        const form = e.target;
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        
        // Mostrar loading
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="loading"></span> A processar...';
        
        // Preparar dados
        const formData = new FormData(form);
        formData.append('csrf_token', this.config.csrfToken);
        
        fetch(form.action, {
            method: form.method,
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showAlert('success', data.message);
                
                // Redirecionar se especificado
                if (data.redirect) {
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1000);
                }
                
                // Reset form se especificado
                if (data.reset_form) {
                    form.reset();
                }
            } else {
                this.showAlert('danger', data.message);
                
                // Mostrar erros de campo
                if (data.errors) {
                    this.showFieldErrors(form, data.errors);
                }
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            this.showAlert('danger', 'Erro interno. Tente novamente.');
        })
        .finally(() => {
            // Restaurar botão
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    },
    
    // Processar ações de confirmação
    handleConfirmAction: function(e) {
        e.preventDefault();

        const element = e.target;
        const message = element.getAttribute('data-confirm');

        this.showConfirmation(
            message,
            () => {
                // User confirmed - proceed with the action
                if (element.tagName === 'A') {
                    window.location.href = element.href;
                } else if (element.tagName === 'BUTTON' && element.form) {
                    element.form.submit();
                } else if (element.onclick) {
                    element.onclick();
                }
            }
            // onCancel is optional - modal will just close
        );

        return false;
    },
    
    // Validar formulário
    validateForm: function(e) {
        const form = e.target;
        let isValid = true;
        
        form.querySelectorAll('input[required], textarea[required], select[required]').forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            this.showAlert('warning', 'Por favor, corrija os erros no formulário.');
        }
        
        return isValid;
    },
    
    // Validar campo individual
    validateField: function(field) {
        const value = field.value.trim();
        let isValid = true;
        let message = '';
        
        // Campo obrigatório
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            message = 'Este campo é obrigatório.';
        }
        
        // Email
        if (field.type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                message = 'Email inválido.';
            }
        }
        
        // Password
        if (field.type === 'password' && value && field.hasAttribute('data-min-length')) {
            const minLength = parseInt(field.getAttribute('data-min-length'));
            if (value.length < minLength) {
                isValid = false;
                message = `A password deve ter pelo menos ${minLength} caracteres.`;
            }
        }
        
        // Confirmar password
        if (field.hasAttribute('data-confirm-password')) {
            const passwordField = document.getElementById(field.getAttribute('data-confirm-password'));
            if (passwordField && value !== passwordField.value) {
                isValid = false;
                message = 'As passwords não coincidem.';
            }
        }
        
        this.setFieldValidation(field, isValid, message);
        return isValid;
    },
    
    // Definir estado de validação do campo
    setFieldValidation: function(field, isValid, message) {
        field.classList.remove('is-valid', 'is-invalid', 'error');

        // Remove any existing tooltips
        this.removeFieldTooltip(field);

        if (!isValid) {
            field.classList.add('error');

            // Set custom validation message for browser tooltip
            field.setCustomValidity(message || 'Este campo é obrigatório.');

            if (message) {
                this.showFieldTooltip(field, message);
            }
        } else {
            field.setCustomValidity('');
            if (field.value.trim()) {
                field.classList.add('is-valid');
            }
        }
    },
    
    // Limpar erro do campo
    clearFieldError: function(field) {
        field.classList.remove('is-invalid', 'error');
        field.setCustomValidity('');
        this.removeFieldTooltip(field);
    },

    // Mostrar tooltip de erro no campo
    showFieldTooltip: function(field, message) {
        // Remove any existing tooltip first
        this.removeFieldTooltip(field);

        const tooltip = document.createElement('div');
        tooltip.className = 'field-error-tooltip';
        tooltip.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;

        // Position tooltip
        const container = field.closest('.form-group') || field.closest('.mb-3') || field.parentNode;
        container.style.position = 'relative';

        tooltip.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #dc3545;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            margin-top: 2px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            animation: fadeInTooltip 0.3s ease-in-out;
        `;

        container.appendChild(tooltip);

        // Auto-hide tooltip after 4 seconds
        setTimeout(() => {
            if (tooltip.parentNode) {
                tooltip.style.animation = 'fadeOutTooltip 0.3s ease-in-out';
                setTimeout(() => tooltip.remove(), 300);
            }
        }, 4000);
    },

    // Remover tooltip de erro do campo
    removeFieldTooltip: function(field) {
        const container = field.closest('.form-group') || field.closest('.mb-3') || field.parentNode;
        const existingTooltip = container.querySelector('.field-error-tooltip');
        if (existingTooltip) {
            existingTooltip.remove();
        }
    },
    
    // Atualizar indicador de força da password
    updatePasswordStrength: function(input, container) {
        const password = input.value;
        const strength = this.calculatePasswordStrength(password);
        
        const fill = container.querySelector('.password-strength-fill');
        const text = container.querySelector('.password-strength-text');
        
        fill.className = 'password-strength-fill';
        
        if (password.length === 0) {
            text.textContent = '';
            return;
        }
        
        if (strength < 25) {
            fill.classList.add('password-strength-weak');
            text.textContent = 'Fraca';
        } else if (strength < 50) {
            fill.classList.add('password-strength-fair');
            text.textContent = 'Razoável';
        } else if (strength < 75) {
            fill.classList.add('password-strength-good');
            text.textContent = 'Boa';
        } else {
            fill.classList.add('password-strength-strong');
            text.textContent = 'Forte';
        }
    },
    
    // Calcular força da password
    calculatePasswordStrength: function(password) {
        let score = 0;
        
        if (password.length >= 8) score += 25;
        if (password.length >= 12) score += 25;
        if (/[a-z]/.test(password)) score += 10;
        if (/[A-Z]/.test(password)) score += 10;
        if (/[0-9]/.test(password)) score += 10;
        if (/[^a-zA-Z0-9]/.test(password)) score += 20;
        
        return Math.min(100, score);
    },
    
    // Mostrar alerta (legacy method - redirects to showNotification)
    showAlert: function(type, message) {
        this.showNotification(message, type);
    },

    // Show styled notification (toast)
    showNotification: function(message, type = 'info', duration = 5000, showProgress = false) {
        const container = document.getElementById('notification-container');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `toast align-items-center text-white bg-${type} border-0 show`;
        notification.setAttribute('role', 'alert');
        notification.setAttribute('aria-live', 'assertive');
        notification.setAttribute('aria-atomic', 'true');

        const iconMap = {
            'success': 'ri-check-line',
            'danger': 'ri-error-warning-line',
            'warning': 'ri-alert-line',
            'info': 'ri-information-line',
            'primary': 'ri-information-line'
        };

        const icon = iconMap[type] || 'ri-information-line';

        // Create progress indicator for backup operations
        const progressIndicator = showProgress ? `
            <div class="notification-progress">
                <div class="progress-dots">
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                </div>
            </div>
        ` : '';

        notification.innerHTML = `
            <div class="d-flex align-items-center w-100">
                <div class="toast-body flex-grow-1">
                    <div class="d-flex align-items-center">
                        <i class="${icon} me-2"></i>
                        <span class="notification-message">${message}</span>
                    </div>
                    ${progressIndicator}
                </div>
                <button type="button" class="btn-close btn-close-white ms-2" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;

        container.appendChild(notification);

        // Add click handler for close button
        const closeBtn = notification.querySelector('.btn-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.removeNotification(notification);
            });
        }

        // Auto-remove after duration (only if duration > 0)
        if (duration > 0) {
            setTimeout(() => {
                this.removeNotification(notification);
            }, duration);
        }

        return notification;
    },

    // Remove notification with animation
    removeNotification: function(notification) {
        if (notification && notification.parentNode) {
            notification.classList.remove('show');
            notification.classList.add('hiding');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }
    },

    // Update notification message and progress
    updateNotification: function(notification, message, showProgress = false) {
        if (!notification) return;

        const messageElement = notification.querySelector('.notification-message');
        const progressElement = notification.querySelector('.notification-progress');

        if (messageElement) {
            messageElement.textContent = message;
        }

        if (showProgress && !progressElement) {
            const toastBody = notification.querySelector('.toast-body');
            const progressHtml = `
                <div class="notification-progress">
                    <div class="progress-dots">
                        <span class="dot"></span>
                        <span class="dot"></span>
                        <span class="dot"></span>
                    </div>
                </div>
            `;
            toastBody.insertAdjacentHTML('beforeend', progressHtml);
        } else if (!showProgress && progressElement) {
            progressElement.remove();
        }
    },

    // Show confirmation dialog
    showConfirmation: function(message, onConfirm, onCancel, title = 'Confirmação') {
        const modal = document.getElementById('confirmation-modal');
        const messageEl = document.getElementById('confirmation-message');
        const confirmBtn = document.getElementById('confirmation-confirm');
        const titleEl = modal.querySelector('.modal-title');

        if (!modal || !messageEl || !confirmBtn || !titleEl) return;

        titleEl.textContent = title;
        messageEl.textContent = message;

        // Remove existing event listeners
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

        // Add new event listeners
        newConfirmBtn.addEventListener('click', () => {
            if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                const modalInstance = bootstrap.Modal.getInstance(modal);
                if (modalInstance) modalInstance.hide();
            }
            if (onConfirm) onConfirm();
        });

        modal.addEventListener('hidden.bs.modal', function handler() {
            modal.removeEventListener('hidden.bs.modal', handler);
            if (onCancel) onCancel();
        });

        // Show modal
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();
        }
    },
    
    // Criar container de alertas
    createAlertContainer: function() {
        const container = document.createElement('div');
        container.id = 'alert-container';
        container.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        document.body.appendChild(container);
        return container;
    },
    
    // Mostrar erros de campo
    showFieldErrors: function(form, errors) {
        Object.keys(errors).forEach(fieldName => {
            const field = form.querySelector(`[name="${fieldName}"]`);
            if (field) {
                this.setFieldValidation(field, false, errors[fieldName]);
            }
        });
    }
};

// Inicializar quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    AdminPanel.init();
});
