<?php
/**
 * Backup Download Handler
 * Santa Casa da Misericórdia da Covilhã
 */

// Inicializar configuração
require_once '../config/config.php';

// Inicializar autenticação
$auth = new Auth();

// Verificar se o utilizador está autenticado
if (!$auth->isLoggedIn()) {
    header('Location: ../login.php');
    exit();
}

// Verificar permissões
if (!$auth->hasPermission('settings.edit')) {
    header('Location: ../unauthorized.php');
    exit();
}

$filename = $_GET['file'] ?? '';

if (empty($filename)) {
    http_response_code(400);
    die('Filename not specified');
}

try {
    $backupManager = new BackupManager();
    $backupManager->downloadBackup($filename);
    
    // Log da ação
    if (class_exists('AuditLog')) {
        $auditLog = new AuditLog();
        $currentUser = $auth->getCurrentUser();
        $auditLog->log(
            $currentUser['id'],
            'backup_download',
            'system',
            null,
            null,
            ['filename' => $filename]
        );
    }
    
} catch (Exception $e) {
    http_response_code(404);
    die('Backup file not found: ' . htmlspecialchars($e->getMessage()));
}
?>
