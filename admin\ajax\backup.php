<?php
/**
 * AJAX Backup Handler
 * Santa Casa da Misericórdia da Covilhã
 */

// Inicializar configuração
require_once '../config/config.php';

// Set memory and time limits for backup operations
ini_set('memory_limit', '1G');
set_time_limit(600); // 10 minutes

// Set JSON header
header('Content-Type: application/json');

// Verificar se é uma requisição AJAX
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
    http_response_code(403);
    exit('Access denied');
}

// Inicializar autenticação
$auth = new Auth();

// Verificar se o utilizador está autenticado
if (!$auth->isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Não autenticado']);
    exit();
}

// Verificar permissões
if (!$auth->hasPermission('settings.edit')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Sem permissões']);
    exit();
}

// Obter dados JSON
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Dados inválidos']);
    exit();
}

// Verificar CSRF token
if (!verifyCSRFToken($input['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Token CSRF inválido']);
    exit();
}

$action = $input['action'] ?? '';
$type = $input['type'] ?? '';

try {
    if ($action === 'create_backup') {
        $backupManager = new BackupManager();

        switch ($type) {
            case 'database':
                $result = $backupManager->createDatabaseBackup();
                break;
            case 'files':
                $result = $backupManager->createFilesBackup();
                break;
            case 'complete':
                $result = $backupManager->createCompleteBackup();
                break;
            default:
                throw new Exception('Tipo de backup inválido');
        }
    } elseif ($action === 'cleanup_backup') {
        $filename = $input['filename'] ?? '';
        if (empty($filename)) {
            throw new Exception('Nome do ficheiro não fornecido');
        }

        $backupManager = new BackupManager();
        $result = $backupManager->cleanupBackupFile($filename);

        echo json_encode($result);
        exit;
    } elseif ($action === 'cleanup_old_backups') {
        $backupManager = new BackupManager();
        $result = $backupManager->cleanupOldBackups();

        echo json_encode($result);
        exit;
    } else {
        throw new Exception('Ação inválida');
    }

    // Only execute this for create_backup action
    if ($action === 'create_backup') {
        
        if ($result['success']) {
            // Log da ação
            if (class_exists('AuditLog')) {
                $auditLog = new AuditLog();
                $currentUser = $auth->getCurrentUser();
                $auditLog->log(
                    $currentUser['id'],
                    'backup_create',
                    'system',
                    null,
                    null,
                    [
                        'type' => $type,
                        'filename' => $result['filename'],
                        'size' => $result['size']
                    ]
                );
            }
            
            echo json_encode([
                'success' => true,
                'filename' => $result['filename'],
                'size' => formatBytes($result['size']),
                'download_url' => 'ajax/download-backup.php?file=' . urlencode($result['filename']),
                'message' => 'Backup criado com sucesso'
            ]);
        } else {
            throw new Exception($result['error']);
        }
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Format bytes to human readable format
 */
function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}
?>
