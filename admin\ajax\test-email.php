<?php
/**
 * AJAX Email Test Handler
 * Santa Casa da Misericórdia da Covilhã
 */

// Start output buffering to prevent any accidental output
ob_start();

// Disable ALL error output to prevent HTML output
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
ini_set('log_errors', 1);
error_reporting(0);

// Set memory and time limits
ini_set('memory_limit', '256M');
set_time_limit(60);

// Set JSON header
header('Content-Type: application/json');

// Function to send JSON response and exit cleanly
function sendJsonResponse($data) {
    // Clear any buffered output
    ob_clean();
    echo json_encode($data);
    exit;
}

// Function to handle errors and send JSON error response
function handleError($message, $details = []) {
    sendJsonResponse([
        'success' => false,
        'error' => $message,
        'details' => $details
    ]);
}

// Set error handler to catch any PHP errors
set_error_handler(function($severity, $message, $file, $line) {
    handleError('PHP Error: ' . $message, [
        'file' => basename($file),
        'line' => $line,
        'severity' => $severity
    ]);
});

// Set exception handler to catch any uncaught exceptions
set_exception_handler(function($exception) {
    handleError('Exception: ' . $exception->getMessage(), [
        'file' => basename($exception->getFile()),
        'line' => $exception->getLine(),
        'type' => get_class($exception)
    ]);
});

try {
    // Inicializar configuração
    require_once __DIR__ . '/../config/config.php';

    // Verificar se é uma requisição AJAX
    if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
        http_response_code(403);
        handleError('Access denied');
    }

    // Inicializar autenticação
    $auth = new Auth();

    // Verificar se o utilizador está autenticado
    if (!$auth->isLoggedIn()) {
        http_response_code(401);
        handleError('Não autenticado');
    }

    // Verificar permissões
    if (!$auth->hasPermission('settings.edit')) {
        http_response_code(403);
        handleError('Sem permissões');
    }

    // Verificar CSRF token
    $csrfToken = $_POST['csrf_token'] ?? '';
    if (!verifyCSRFToken($csrfToken)) {
        http_response_code(403);
        handleError('Token CSRF inválido');
    }

    // Processar apenas requisições POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        handleError('Método não permitido');
    }

    // Initialize EmailManager and validate input
    $emailManager = new EmailManager();
    $testEmail = trim($_POST['test_email'] ?? '');

    // Validate test email
    if (empty($testEmail)) {
        sendJsonResponse([
            'success' => false,
            'error' => 'Email de teste é obrigatório',
            'step' => 'input_validation'
        ]);
    }

    if (!filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
        sendJsonResponse([
            'success' => false,
            'error' => 'Email de teste deve ser um endereço válido',
            'step' => 'input_validation'
        ]);
    }

    // Get current form configuration (not saved configuration)
    $currentConfig = [
        'host' => trim($_POST['smtp_host'] ?? ''),
        'port' => intval($_POST['smtp_port'] ?? 587),
        'username' => trim($_POST['smtp_username'] ?? ''),
        'password' => $_POST['smtp_password'] ?? '',
        'encryption' => trim($_POST['smtp_encryption'] ?? 'tls'),
        'from_email' => trim($_POST['smtp_from_email'] ?? ''),
        'from_name' => trim($_POST['smtp_from_name'] ?? '')
    ];

    // Debug: Log received configuration (without password)
    error_log("Email test - Received config: " . json_encode([
        'host' => $currentConfig['host'],
        'port' => $currentConfig['port'],
        'username' => $currentConfig['username'],
        'password' => !empty($currentConfig['password']) ? '[SET]' : '[EMPTY]',
        'encryption' => $currentConfig['encryption'],
        'from_email' => $currentConfig['from_email'],
        'from_name' => $currentConfig['from_name']
    ]));

    // Test email configuration with current form values
    $result = $emailManager->testEmailConfigWithValues($currentConfig, $testEmail);

    // Add timestamp to result if successful
    if ($result['success'] && isset($result['details'])) {
        $result['details']['timestamp'] = date('d/m/Y H:i:s');
    }

    // Return the complete result as JSON
    sendJsonResponse($result);

} catch (Exception $e) {
    error_log("Email test error: " . $e->getMessage());
    handleError('Erro interno: ' . $e->getMessage(), [
        'step' => 'exception',
        'file' => basename($e->getFile()),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
}
?>
