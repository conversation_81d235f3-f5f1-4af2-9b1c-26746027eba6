/**
 * Dashboard JavaScript
 * Santa Casa da Misericórdia da Covilhã
 */

(function($) {
    "use strict";

    function Dashboard() {
        this.$body = $("body");
        this.charts = [];
    }

    Dashboard.prototype.initCharts = function() {
        // Set global Apex chart configuration
        if (typeof window.Apex !== 'undefined') {
            window.Apex = {
                chart: {
                    parentHeightOffset: 0,
                    toolbar: {
                        show: false
                    }
                },
                grid: {
                    padding: {
                        left: 0,
                        right: 0
                    }
                },
                colors: ["#2c5aa0", "#47ad77", "#fa5c7c", "#ffbc00"]
            };
        }

        // Only initialize charts if ApexCharts is available and elements exist
        if (typeof ApexCharts === 'undefined') {
            console.warn('ApexCharts library not loaded');
            return;
        }

        // Revenue Chart
        this.initRevenueChart();

        // Sales Chart
        this.initSalesChart();

        // Share Chart
        this.initShareChart();
    };

    Dashboard.prototype.initRevenueChart = function() {
        const chartElement = document.querySelector("#revenue-chart");
        if (!chartElement) {
            console.log('Revenue chart element not found - skipping');
            return;
        }

        const colors = ["#2c5aa0", "#47ad77", "#fa5c7c", "#ffbc00"];
        const dataColors = $(chartElement).data("colors");
        const chartColors = dataColors ? dataColors.split(",") : colors;

        // Get data from PHP if available, otherwise use demo data
        const userData = window.dashboardData?.users || [44, 55, 41, 52, 22, 41, 20];
        const sessionData = window.dashboardData?.sessions || [32, 25, 36, 45, 20, 36, 38];
        const activityData = window.dashboardData?.activity || [32, 45, 36, 52, 18, 36, 16];

        const options = {
            series: [{
                name: "Utilizadores",
                data: userData
            }, {
                name: "Sessões",
                data: sessionData
            }, {
                name: "Atividade",
                data: activityData
            }],
            chart: {
                height: 377,
                type: "bar"
            },
            plotOptions: {
                bar: {
                    columnWidth: "60%"
                }
            },
            stroke: {
                show: true,
                width: 2,
                colors: ["transparent"]
            },
            dataLabels: {
                enabled: false
            },
            colors: chartColors,
            xaxis: {
                categories: ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"]
            },
            yaxis: {
                title: {
                    text: "Contagem"
                }
            },
            legend: {
                offsetY: 7
            },
            grid: {
                padding: {
                    bottom: 20
                }
            },
            fill: {
                opacity: 1
            },
            tooltip: {
                y: {
                    formatter: function(val) {
                        return val + " registos";
                    }
                }
            }
        };

        try {
            const chart = new ApexCharts(chartElement, options);
            chart.render();
            this.charts.push(chart);
        } catch (error) {
            console.error('Error rendering revenue chart:', error);
        }
    };

    Dashboard.prototype.initSalesChart = function() {
        const chartElement = document.querySelector("#yearly-sales-chart");
        if (!chartElement) {
            console.log('Sales chart element not found - skipping');
            return;
        }

        const colors = ["#2c5aa0", "#47ad77"];
        const dataColors = $(chartElement).data("colors");
        const chartColors = dataColors ? dataColors.split(",") : colors;

        const options = {
            series: [{
                name: "Mobile",
                data: [25, 15, 25, 36, 32, 42, 45]
            }, {
                name: "Desktop",
                data: [20, 10, 20, 31, 27, 37, 40]
            }],
            chart: {
                height: 250,
                type: "line",
                toolbar: {
                    show: false
                }
            },
            colors: chartColors,
            stroke: {
                curve: "smooth",
                width: [3, 3]
            },
            markers: {
                size: 3
            },
            xaxis: {
                categories: ["2017", "2018", "2019", "2020", "2021", "2022", "2023"]
            },
            legend: {
                show: false
            }
        };

        try {
            const chart = new ApexCharts(chartElement, options);
            chart.render();
            this.charts.push(chart);
        } catch (error) {
            console.error('Error rendering sales chart:', error);
        }
    };

    Dashboard.prototype.initShareChart = function() {
        const chartElement = document.querySelector("#us-share-chart");
        if (!chartElement) {
            console.log('Share chart element not found - skipping');
            return;
        }

        const options = {
            series: [44, 55, 13, 43],
            chart: {
                width: 80,
                type: "pie"
            },
            legend: {
                show: false
            },
            dataLabels: {
                enabled: false
            },
            colors: ["#2c5aa0", "#f13c6e", "#3bc0c3", "#d1d7d973"],
            labels: ["Admin", "Editor", "Viewer", "Guest"]
        };

        try {
            const chart = new ApexCharts(chartElement, options);
            chart.render();
            this.charts.push(chart);
        } catch (error) {
            console.error('Error rendering share chart:', error);
        }
    };

    Dashboard.prototype.init = function() {
        this.initCharts();
    };

    // Create global Dashboard object
    $.Dashboard = new Dashboard();
    $.Dashboard.Constructor = Dashboard;

})(window.jQuery);

// Initialize when document is ready
(function($) {
    "use strict";
    $(document).ready(function() {
        if (typeof $.Dashboard !== 'undefined') {
            $.Dashboard.init();
        }
    });
})(window.jQuery);